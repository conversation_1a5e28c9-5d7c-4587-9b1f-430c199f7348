import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { requestOtp, login as loginApi } from '@/services/api';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtp, setShowOtp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      await requestOtp(email);
      setShowOtp(true);
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Failed to send OTP');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const res = await loginApi(email, otp);
      if (res.data.success) {
        navigate('/dashboard');
      } else {
        setError(res.data.message || 'Invalid OTP');
      }
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Invalid OTP');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-accent-400/30 to-neutral-900">
      <form
        onSubmit={showOtp ? handleVerifyOtp : handleSendOtp}
        className="bg-neutral-950 shadow-2xl rounded-2xl p-10 flex flex-col gap-6 w-full max-w-md border border-neutral-800"
      >
        <div className="text-3xl font-extrabold text-center bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-2">
          ZiaHR Admin Login
        </div>
        <div className="text-neutral-400 text-center mb-4">Sign in to your admin account</div>
        <input
          type="email"
          placeholder="Admin Email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
          required
          disabled={showOtp}
        />
        {showOtp && (
          <input
            type="text"
            placeholder="OTP"
            value={otp}
            onChange={e => setOtp(e.target.value)}
            className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
            required
            autoFocus
          />
        )}
        {error && <div className="text-red-500 text-center font-semibold">{error}</div>}
        <button
          type="submit"
          className="bg-accent-400 hover:bg-accent-600 text-neutral-900 font-bold rounded py-2 text-lg transition"
          disabled={loading}
        >
          {loading ? (showOtp ? 'Verifying...' : 'Send OTP') : (showOtp ? 'Verify OTP' : 'Send OTP')}
        </button>
        <div className="flex justify-between text-xs text-neutral-500 mt-2">
          <a href="#" className="hover:underline">Forgot password?</a>
          <a href="#" className="hover:underline">Create Account</a>
        </div>
        <div className="text-xs text-neutral-500 text-center mt-2">
          Only admin users are allowed. Unauthorized access is prohibited.
        </div>
      </form>
    </div>
  );
} 