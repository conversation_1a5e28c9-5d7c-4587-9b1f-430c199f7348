import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select"; // Assume stub
import { Toggle } from "@/components/ui/toggle";
import { Skeleton } from "@/components/ui/skeleton";
import { useThemeStore } from "@/hooks/useThemeStore";

const FREQUENCIES = ["Daily", "Weekly", "Monthly"];

const SettingsPanel = () => {
  const { theme, toggleTheme } = useThemeStore();
  const [frequency, setFrequency] = React.useState("Weekly");
  const [time, setTime] = React.useState("09:00");
  const [recipients, setRecipients] = React.useState("");
  const [saving, setSaving] = React.useState(false);

  const handleSave = () => {
    setSaving(true);
    setTimeout(() => {
      setSaving(false);
      alert("Settings saved (mock)");
    }, 800);
  };
  const handleExportPDF = () => {
    alert("Export Settings to PDF (mock)");
  };
  const handleExportCSV = () => {
    alert("Export Settings to CSV (mock)");
  };

  return (
    <div className="max-w-3xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-semibold">Theme</span>
              <Toggle checked={theme === "dark"} onCheckedChange={v => toggleTheme()}>
                Dark Mode
              </Toggle>
            </div>
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <span className="text-sm font-semibold">Email Scheduler</span>
              <Select value={frequency} onValueChange={setFrequency}>
                {FREQUENCIES.map(opt => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </Select>
              <Input
                type="time"
                value={time}
                onChange={e => setTime(e.target.value)}
                className="w-28"
              />
              <Input
                type="text"
                placeholder="Recipients (comma-separated emails)"
                value={recipients}
                onChange={e => setRecipients(e.target.value)}
                className="w-64"
              />
              <Button size="sm" variant="default" onClick={handleSave} disabled={saving}>
                {saving ? "Saving..." : "Save"}
              </Button>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={handleExportPDF}>Export Settings PDF</Button>
              <Button size="sm" variant="outline" onClick={handleExportCSV}>Export Settings CSV</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SettingsPanel; 