import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Check, User, Bo<PERSON>, MoreVertical, ThumbsUp, ThumbsDown, FileText } from 'lucide-react';
import { Message } from '@/types';
import { Button } from '@/components/ui/button';
import { cn, formatTime, copyToClipboard, formatFileSize } from '@/lib/utils';

interface MessageBubbleProps {
  message: Message;
  isGrouped?: boolean;
  showAvatar?: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isGrouped = false,
  showAvatar = true
}) => {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
  const isUser = message.isUser || message.sender === 'user';

  const handleCopy = async () => {
    try {
      await copyToClipboard(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleFeedback = (type: 'up' | 'down') => {
    setFeedback(feedback === type ? null : type);
    // TODO: Send feedback to backend
  };

  const renderMessageContent = (content: string) => {
    const lines = content.split('\n');
    return lines.map((line, index) => (
      <div key={index} className="mb-1 last:mb-0">
        {line}
      </div>
    ));
  };

  const renderFileAttachments = () => {
    if (!message.files || message.files.length === 0) return null;

    return (
      <div className="mt-3 space-y-2">
        {message.files.map((file) => (
          <div key={file.id} className="flex items-center gap-2 rounded-lg bg-muted/50 p-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{file.name}</span>
            <span className="text-xs text-muted-foreground">
              {formatFileSize(file.size)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  const messageVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    exit: { opacity: 0, y: -20, scale: 0.95 }
  };

  return (
    <motion.div
      variants={messageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={cn(
        "group flex gap-3 px-4 py-3 hover:bg-muted/30 transition-colors duration-200",
        isUser ? "flex-row-reverse" : "flex-row",
        !isGrouped && "mt-6"
      )}
    >
      {/* Avatar */}
      {showAvatar && !isGrouped && (
        <div className={cn(
          "flex h-8 w-8 shrink-0 items-center justify-center rounded-full",
          isUser
            ? "bg-primary text-primary-foreground"
            : "bg-muted text-muted-foreground"
        )}>
          {isUser ? (
            <User className="h-4 w-4" />
          ) : (
            <Bot className="h-4 w-4" />
          )}
        </div>
      )}

      {/* Spacer for grouped messages */}
      {isGrouped && showAvatar && (
        <div className="w-8 shrink-0" />
      )}

      {/* Message Content */}
      <div className={cn(
        "flex flex-col gap-1 min-w-0 flex-1",
        isUser ? "items-end" : "items-start"
      )}>
        {/* Message Bubble */}
        <div className={cn(
          "chat-bubble relative group/bubble",
          isUser ? "chat-bubble-user" : "chat-bubble-assistant",
          "max-w-[85%] md:max-w-[70%]"
        )}>
          <div className="prose prose-sm max-w-none dark:prose-invert">
            {renderMessageContent(message.content)}
          </div>

          {/* File Attachments */}
          {renderFileAttachments()}

          {/* Message Actions */}
          <div className={cn(
            "absolute top-2 opacity-0 group-hover/bubble:opacity-100 transition-opacity duration-200",
            isUser ? "-left-12" : "-right-12"
          )}>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={handleCopy}
                className="h-6 w-6 bg-background/80 backdrop-blur-sm border shadow-soft hover:bg-background"
              >
                {copied ? (
                  <Check className="h-3 w-3 text-success-500" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                className="h-6 w-6 bg-background/80 backdrop-blur-sm border shadow-soft hover:bg-background"
              >
                <MoreVertical className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Message Footer */}
        <div className={cn(
          "flex items-center gap-2 px-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",
          isUser ? "flex-row-reverse" : "flex-row"
        )}>
          {/* Timestamp */}
          <span className="text-xs text-muted-foreground">
            {formatTime(new Date(message.timestamp))}
          </span>

          {/* Feedback buttons for assistant messages */}
          {!isUser && (
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => handleFeedback('up')}
                className={cn(
                  "h-6 w-6",
                  feedback === 'up' && "text-success-500 bg-success-50"
                )}
              >
                <ThumbsUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => handleFeedback('down')}
                className={cn(
                  "h-6 w-6",
                  feedback === 'down' && "text-destructive bg-destructive/10"
                )}
              >
                <ThumbsDown className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default MessageBubble;
