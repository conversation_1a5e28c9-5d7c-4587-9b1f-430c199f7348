import { useAuth } from '../hooks/useAuth';
import { RoleBadge } from './RoleBadge';

export function Topbar() {
  const { user, role, logout } = useAuth();
  return (
    <header className="flex items-center justify-between bg-white border-b px-6 py-3">
      <div className="font-bold text-lg">HR Chatbot Admin</div>
      <div className="flex items-center gap-4">
        {user && <span className="text-gray-700">{user}</span>}
        {role && <RoleBadge role={role} />}
        <button onClick={logout} className="ml-4 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition">Logout</button>
      </div>
    </header>
  );
} 