import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Suggestion {
  text: string;
  query: string;
  icon?: string;
  category?: string;
}

interface SuggestionChipsProps {
  suggestions?: Suggestion[];
  onSuggestionClick: (query: string) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

const SuggestionChips: React.FC<SuggestionChipsProps> = ({
  suggestions,
  onSuggestionClick,
  className,
  variant = 'default'
}) => {
  const defaultSuggestions: Suggestion[] = [
    {
      text: "Leave Policy",
      query: "What is the company's leave policy?",
      icon: "🗓️",
      category: "Policies"
    },
    {
      text: "Referral Program",
      query: "How does the employee referral program work?",
      icon: "👥",
      category: "Programs"
    },
    {
      text: "Dress Code",
      query: "What is the dress code policy?",
      icon: "👔",
      category: "Policies"
    },
    {
      text: "Work from Home",
      query: "Tell me about the work from home policy",
      icon: "🏠",
      category: "Policies"
    },
    {
      text: "Benefits",
      query: "What are the company benefits?",
      icon: "💼",
      category: "Benefits"
    },
    {
      text: "Request Time Off",
      query: "How do I request time off?",
      icon: "📅",
      category: "Actions"
    },
  ];

  const chipSuggestions = suggestions || defaultSuggestions;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const chipVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    },
    tap: {
      scale: 0.95
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "flex flex-wrap gap-3",
        variant === 'compact' ? "gap-2" : "gap-3",
        className
      )}
    >
      {chipSuggestions.map((suggestion, index) => (
        <motion.div
          key={index}
          variants={chipVariants}
          whileHover="hover"
          whileTap="tap"
        >
          <Button
            variant="outline"
            size={variant === 'compact' ? 'sm' : 'default'}
            onClick={() => onSuggestionClick(suggestion.query)}
            className={cn(
              "suggestion-chip h-auto py-2 px-4 rounded-full border-2 transition-all duration-200",
              "hover:border-primary hover:bg-primary hover:text-primary-foreground",
              "focus:ring-2 focus:ring-primary focus:ring-offset-2",
              variant === 'compact' && "py-1 px-3 text-xs"
            )}
          >
            <span className="flex items-center gap-2">
              {suggestion.icon && (
                <span className="text-base" role="img" aria-label={suggestion.category}>
                  {suggestion.icon}
                </span>
              )}
              <span className="font-medium">{suggestion.text}</span>
            </span>
          </Button>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default SuggestionChips;
