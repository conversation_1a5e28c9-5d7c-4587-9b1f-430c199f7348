import React, { useState, useEffect, use<PERSON>em<PERSON>, use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useReactTable, getCoreRowModel, flexRender, ColumnDef, getSortedRowModel, SortingState } from "@tanstack/react-table";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import api from "@/services/api";
import { LogOut, AlertTriangle, Monitor, Lock, Bell, XOctagon } from "lucide-react";
import 'leaflet/dist/leaflet.css';

const USER_TYPE_OPTIONS = [
  { label: "Admin", value: "admin" },
  { label: "Chatbot User", value: "chatbot_user" },
];

const UserTypeBadge = ({ userType }: { userType: string }) => (
  <span
    className={`inline-block px-2 py-0.5 rounded text-xs font-semibold ml-2 ${
      userType === "admin"
        ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
        : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    }`}
  >
    {userType === "admin" ? "Admin" : "Chatbot User"}
  </span>
);

// --- Live Sessions Tab ---
const LiveSessionsTab = ({ userType }: { userType: string }) => {
  const [sessions, setSessions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingId, setLoadingId] = useState<string | null>(null);

  const fetchSessions = useCallback(() => {
    setLoading(true);
    setError(null);
    api.get(`/api/sessions/active?user_type=${userType}`)
      .then(res => setSessions(res.data.sessions || []))
      .catch(() => setError("Failed to load sessions"))
      .finally(() => setLoading(false));
  }, [userType]);

  useEffect(() => {
    fetchSessions();
    const interval = setInterval(fetchSessions, 10000);
    return () => clearInterval(interval);
  }, [fetchSessions]);

  const handleTerminate = async (session: any) => {
    setLoadingId(session.id);
    try {
      await api.post(`/api/sessions/${session.id}/terminate`);
      fetchSessions();
    } catch {
      // Optionally show toast
    } finally {
      setLoadingId(null);
    }
  };

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!sessions.length) return <div className="text-muted-foreground p-4">No active sessions.</div>;

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
        <thead className="bg-muted dark:bg-zinc-800">
          <tr>
            <th className="px-4 py-2">User</th>
            <th className="px-4 py-2">IP</th>
            <th className="px-4 py-2">Browser</th>
            <th className="px-4 py-2">OS</th>
            <th className="px-4 py-2">Device Fingerprint</th>
            <th className="px-4 py-2">Login Time</th>
            <th className="px-4 py-2">Last Activity</th>
            <th className="px-4 py-2">Idle</th>
            <th className="px-4 py-2">Location</th>
            <th className="px-4 py-2">Status</th>
            <th className="px-4 py-2">User Type</th>
            <th className="px-4 py-2">Actions</th>
          </tr>
        </thead>
        <tbody>
          {sessions.map((session: any) => {
            const compromised = session.device_fingerprint_mismatch || session.compromised;
            return (
              <tr key={session.id} className={compromised ? "bg-red-100 dark:bg-red-900/30" : ""}>
                <td className="px-4 py-2 font-mono text-xs">{session.username || session.user_email}</td>
                <td className="px-4 py-2">{session.ip_address}</td>
                <td className="px-4 py-2">{session.browser}</td>
                <td className="px-4 py-2">{session.os}</td>
                <td className="px-4 py-2 font-mono text-xs">{session.device_fingerprint}</td>
                <td className="px-4 py-2">{session.login_time}</td>
                <td className="px-4 py-2">{session.last_activity}</td>
                <td className="px-4 py-2">{session.idle_duration}</td>
                <td className="px-4 py-2">{session.location_country}, {session.location_city}</td>
                <td className="px-4 py-2">
                  {compromised ? <span className="text-red-600 font-bold">Compromised</span> : <span className="text-green-600">OK</span>}
                </td>
                <td className="px-4 py-2"><UserTypeBadge userType={session.user_type} /></td>
                <td className="px-4 py-2">
                  {session.user_type === "admin" ? (
                    <Button size="sm" variant="destructive" onClick={() => handleTerminate(session)} disabled={loadingId === session.id}>
                      <LogOut size={16} /> {loadingId === session.id ? "Terminating..." : "Terminate"}
                    </Button>
                  ) : (
                    <span className="text-xs text-muted-foreground">N/A</span>
                  )}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

// --- Historical Logs Tab ---
const PAGE_SIZE = 10;
const HistoricalLogsTab = ({ userType }: { userType: string }) => {
  const [dateRange, setDateRange] = useState("7d");
  const [user, setUser] = useState("");
  const [ip, setIp] = useState("");
  const [authMethod, setAuthMethod] = useState("");
  const [result, setResult] = useState("");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      `user_type=${userType}`,
      `range=${dateRange}`,
      user ? `user_id=${encodeURIComponent(user)}` : null,
      ip ? `ip_address=${encodeURIComponent(ip)}` : null,
      authMethod ? `auth_method=${encodeURIComponent(authMethod)}` : null,
      result ? `success=${encodeURIComponent(result)}` : null,
      `page=${page}`,
      `pageSize=${PAGE_SIZE}`,
    ].filter(Boolean).join("&");
    api.get(`/api/sessions/history?${params}`)
      .then(res => {
        setData(res.data.logs || []);
        setTotal(res.data.total || 0);
      })
      .catch(() => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [userType, dateRange, user, ip, authMethod, result, page]);

  const columns = useMemo<ColumnDef<any>[]>(() => [
    { accessorKey: "timestamp", header: "Timestamp" },
    { accessorKey: "user_email", header: "User" },
    { accessorKey: "device_fingerprint", header: "Device Fingerprint" },
    { accessorKey: "ip_address", header: "IP" },
    { accessorKey: "browser", header: "Browser" },
    { accessorKey: "os", header: "OS" },
    { accessorKey: "location", header: "Location" },
    { accessorKey: "auth_method", header: "Auth Method" },
    { accessorKey: "result", header: "Result" },
    { accessorKey: "user_type", header: "User Type", cell: ({ row }) => <UserTypeBadge userType={row.original.user_type} /> },
  ], []);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false,
  });

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div>
      <div className="flex flex-wrap gap-2 mb-4">
        <Select value={dateRange} onValueChange={setDateRange}>
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="custom">Custom</option>
        </Select>
        <input className="input input-sm border rounded px-2" placeholder="User" value={user} onChange={e => setUser(e.target.value)} />
        <input className="input input-sm border rounded px-2" placeholder="IP" value={ip} onChange={e => setIp(e.target.value)} />
        <input className="input input-sm border rounded px-2" placeholder="Auth Method" value={authMethod} onChange={e => setAuthMethod(e.target.value)} />
        <Select value={result} onValueChange={setResult}>
          <option value="">All Results</option>
          <option value="success">Success</option>
          <option value="failure">Failure</option>
        </Select>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
          <thead className="bg-muted dark:bg-zinc-800">
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th
                    key={header.id}
                    className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase cursor-pointer"
                    onClick={header.column.getToggleSortingHandler?.()}
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                    {header.column.getIsSorted?.() ? (header.column.getIsSorted() === "asc" ? " ▲" : " ▼") : ""}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-background dark:bg-zinc-900">
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className="border-b border-border dark:border-zinc-800">
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className="px-4 py-2 text-sm">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-end gap-2 items-center mt-4">
        <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
          Previous
        </Button>
        <span className="text-xs text-muted-foreground">
          Page {page} of {totalPages || 1}
        </span>
        <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
          Next
        </Button>
      </div>
    </div>
  );
};

// --- Geo Activity Map Tab ---
const GeoActivityMapTab = ({ userType }: { userType: string }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hovered, setHovered] = useState<any | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    api.get(`/api/sessions/locations?user_type=${userType}`)
      .then(res => setData(res.data.locations || []))
      .catch(() => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [userType]);

  // Explicitly type as 'any' due to dynamic require; avoids TS7034/TS7005 errors
  let MapContainer: any, TileLayer: any, Marker: any, Popup: any;
  try {
    // @ts-ignore
    ({ MapContainer, TileLayer, Marker, Popup } = require("react-leaflet"));
  } catch {}

  if (loading) return <Skeleton className="h-96 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  if (MapContainer && TileLayer && Marker && Popup) {
    return (
      <div className="w-full h-96 relative">
        <MapContainer center={[20, 0]} zoom={2} style={{ height: "100%", width: "100%" }}>
          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
          {data.map((loc: any, i: number) => (
            <Marker key={i} position={[loc.latitude, loc.longitude]}>
              <Popup>
                <div>
                  <b>User:</b> {loc.user_id}<br />
                  <b>City:</b> {loc.location_city}<br />
                  <b>Country:</b> {loc.location_country}<br />
                  <b>Time:</b> {loc.login_time}<br />
                  <UserTypeBadge userType={loc.user_type} />
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>
    );
  }

  return (
    <div className="w-full h-96 flex items-center justify-center">
      <span className="text-muted-foreground">Map visualization not available (react-leaflet not installed).</span>
    </div>
  );
};

// --- Threat Detection Tab ---
const ThreatDetectionTab = ({ userType }: { userType: string }) => {
  const [dateRange, setDateRange] = useState("7d");
  const [eventType, setEventType] = useState("All");
  const [severity, setSeverity] = useState("All");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [alerts, setAlerts] = useState<any[]>([]);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      `user_type=${userType}`,
      `range=${dateRange}`,
      eventType !== "All" ? `eventType=${encodeURIComponent(eventType)}` : null,
      severity !== "All" ? `severity=${encodeURIComponent(severity)}` : null,
      `page=${page}`,
      `pageSize=10`,
    ].filter(Boolean).join("&");
    api.get(`/api/sessions/anomalies?${params}`)
      .then(res => {
        setData(res.data.anomalies || []);
        setTotal(res.data.total || 0);
        setAlerts((res.data.anomalies || []).filter((a: any) => a.severity === "High"));
      })
      .catch(() => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [userType, dateRange, eventType, severity, page]);

  const columns = useMemo<ColumnDef<any>[]>(() => [
    { accessorKey: "event_type", header: "Event Type" },
    { accessorKey: "user_email", header: "User Email" },
    { accessorKey: "timestamp", header: "Timestamp" },
    { accessorKey: "severity", header: "Severity" },
    { accessorKey: "description", header: "Description" },
    { accessorKey: "user_type", header: "User Type", cell: ({ row }) => <UserTypeBadge userType={row.original.user_type} /> },
    { accessorKey: "actions", header: "Actions", cell: ({ row }) => (
      <div className="flex gap-2">
        <Button size="xs" variant="destructive"><Lock size={14} /> Lock</Button>
        <Button size="xs" variant="secondary"><Bell size={14} /> Notify</Button>
        <Button size="xs" variant="ghost"><XOctagon size={14} /> Terminate</Button>
      </div>
    ) },
  ], []);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false,
  });

  const renderAlerts = () => alerts.map((alert, i) => (
    <div key={i} className="mb-2 p-3 rounded bg-red-600/10 border border-red-600 text-red-700 dark:bg-red-900/20 dark:text-red-300 flex items-center gap-2">
      <span className="font-bold">High Severity:</span> {alert.description} ({alert.event_type})
    </div>
  ));

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / 10);

  return (
    <div>
      {renderAlerts()}
      <div className="flex flex-wrap gap-2 mb-4">
        <Select value={dateRange} onValueChange={setDateRange}>
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="custom">Custom</option>
        </Select>
        <Select value={eventType} onValueChange={setEventType}>
          <option value="All">All Events</option>
          <option value="Impossible Travel">Impossible Travel</option>
          <option value="Failed Logins">Failed Logins</option>
          <option value="Blacklisted IP">Blacklisted IP</option>
        </Select>
        <Select value={severity} onValueChange={setSeverity}>
          <option value="All">All Severities</option>
          <option value="Low">Low</option>
          <option value="Medium">Medium</option>
          <option value="High">High</option>
        </Select>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
          <thead className="bg-muted dark:bg-zinc-800">
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th
                    key={header.id}
                    className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase cursor-pointer"
                    onClick={header.column.getToggleSortingHandler?.()}
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                    {header.column.getIsSorted?.() ? (header.column.getIsSorted() === "asc" ? " ▲" : " ▼") : ""}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-background dark:bg-zinc-900">
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className="border-b border-border dark:border-zinc-800">
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className="px-4 py-2 text-sm">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-end gap-2 items-center mt-4">
        <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
          Previous
        </Button>
        <span className="text-xs text-muted-foreground">
          Page {page} of {totalPages || 1}
        </span>
        <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
          Next
        </Button>
      </div>
    </div>
  );
};

const DeviceIntelligence: React.FC = () => {
  const [userType, setUserType] = useState("admin");

  return (
    <ProtectedRoute roles={["superadmin", "viewer"]}>
      <div className="max-w-7xl mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Device Intelligence</h1>
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">User Type:</span>
            <Select value={userType} onValueChange={setUserType}>
              {USER_TYPE_OPTIONS.map(opt => (
                <option key={opt.value} value={opt.value}>{opt.label}</option>
              ))}
            </Select>
          </div>
        </div>
        <Tabs defaultValue="live" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="live">Live Sessions</TabsTrigger>
            <TabsTrigger value="history">Historical Logs</TabsTrigger>
            <TabsTrigger value="geo">Geo Activity Map</TabsTrigger>
            <TabsTrigger value="threats">Threat Detection</TabsTrigger>
          </TabsList>
          <TabsContent value="live">
            <Card>
              <CardHeader>
                <CardTitle>Live Sessions</CardTitle>
              </CardHeader>
              <CardContent>
                <LiveSessionsTab userType={userType} />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>Historical Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <HistoricalLogsTab userType={userType} />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="geo">
            <Card>
              <CardHeader>
                <CardTitle>Geo Activity Map</CardTitle>
              </CardHeader>
              <CardContent>
                <GeoActivityMapTab userType={userType} />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="threats">
            <Card>
              <CardHeader>
                <CardTitle>Threat Detection</CardTitle>
              </CardHeader>
              <CardContent>
                <ThreatDetectionTab userType={userType} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
};

export default DeviceIntelligence; 