export type Role = 'admin' | 'superadmin' | 'hr_lead' | 'viewer';

export interface AdminUser {
  email: string;
  role: Role;
  active: boolean;
  full_name: string;
}

export interface AuditLog {
  id: number;
  timestamp: string;
  admin_email: string;
  action: string;
  ip_address: string;
  metadata: any;
}

export interface DeviceLog {
  id: number;
  email: string;
  user_agent: string;
  ip_address: string;
  first_seen: string;
  last_seen: string;
  country?: string;
  region?: string;
  city?: string;
} 