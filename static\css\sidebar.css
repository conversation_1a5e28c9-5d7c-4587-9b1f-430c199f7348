/* Sidebar Styles extracted from ziantrix-style-clean.css */

:root {
    --sidebar-bg: #f9f9f9;
    --sidebar-text: #2d2d2d;
    --sidebar-hover: #f0f0f0;
    --sidebar-border: #e5e5e5;
    --sidebar-item-hover: rgba(0, 0, 0, 0.05);
    --sidebar-item-active: #e5e5e5;
    --sidebar-accent: #2d2d2d;
    --sidebar-width: 258px;
    --sidebar-collapsed-width: 68px;
    --sidebar-min-width: 200px;
    --sidebar-max-width: 280px;
}

.sidebar {
    min-width: 60px;
    max-width: 290px;
    width: var(--sidebar-width, 270px);
    transition: width 0.3s;
    overflow-x: hidden;
    background-color: #fafafa !important;
    border-right: 1px solid var(--sidebar-border);
}

.sidebar.collapsed {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
}

/* Remove all .sidebar ~ .chat-container .chat-input-container transform/left rules */

/* Center .chat-input-container using margin auto and max-width */
.chat-input-container {
    margin: 0 auto !important;
    max-width: 600px !important;
    width: 100% !important;
    left: unset !important;
    right: unset !important;
    transform: none !important;
    position: static !important;
}

/* Responsive: allow .chat-input-container to shrink on small screens */
@media (max-width: 700px) {
    .chat-input-container {
        max-width: 98vw !important;
        width: 98vw !important;
    }
}
@media (max-width: 480px) {
    .chat-input-container {
        max-width: 100vw !important;
        width: 100vw !important;
    }
}

/* .app-container and .chat-container should use flexbox for layout */
.app-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
    width: 100vw;
    max-width: 100vw;
    overflow-x: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: var(--bg-primary);
    box-sizing: border-box;
    width: 100%;
    max-width: 100vw;
}

/* Remove any .sidebar ~ .chat-container .chat-input-container transform/left rules */
/* Remove any .sidebar-collapsed or expanded rules that shift .chat-input-container */

.sidebar.collapsed ~ .chat-container .welcome-container ~ .chat-input-container,
.sidebar:not(.collapsed) ~ .chat-container .welcome-container ~ .chat-input-container {
    transform: translate(-50%, -50%) !important;
}

body.welcome-removed .chat-input-container {
    transform: translate(-50%, 0) !important;
    top: auto !important;
    bottom: 24px !important;
}

.sidebar.hidden {
    display: none;
}

.app-container.sidebar-hidden {
    width: 100%;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 4px 8px;
    border-bottom: none !important;
    border-top: none !important;
    box-shadow: none !important;
    background-color: var(--sidebar-bg);
    min-height: 32px;
}

.sidebar-brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 24px;
    height: 24px;
    margin: 0 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-app-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: contain;
    transition: transform 0.2s ease;
}

.sidebar-new-chat-btn,
#newChatBtn {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
    min-height: 32px !important;
    max-width: 32px !important;
    max-height: 32px !important;
    margin: 0 4px !important;
    padding: 0 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.sidebar-new-chat-btn i,
#newChatBtn i,
.sidebar-new-chat-btn img,
#newChatBtn img {
    font-size: 24px !important;
    width: 24px !important;
    height: 24px !important;
    background: transparent !important;
    box-shadow: none !important;
    filter: none !important;
    border-radius: 0 !important;
}

.sidebar-new-chat-btn:hover {
    background-color: var(--sidebar-item-hover);
    border-color: var(--sidebar-text);
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-left: 0;
    width: 100%;
}

.sidebar-menu-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 18px 10px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--sidebar-text);
    font-size: 15px;
    font-weight: 400;
    border-radius: 16px;
    margin: 4px 0;
    background: transparent;
    gap: 10px;
    text-align: left;
}

.sidebar-menu-item:hover {
    background-color: #f3f3f3;
}

.sidebar-menu-item.active {
    background-color: #ededed;
    font-weight: 500;
}

.sidebar-menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.sidebar-menu-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-section-header {
    position: sticky;
    top: 0;
    z-index: 30;
    background: #fafafa;
    box-shadow: 0 2px 4px -2px rgba(0,0,0,0.04);
    padding: 2px 16px 2px 16px;
    margin-top: 0;
}

.sidebar-section-header::after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -4px;
    height: 12px;
    background: #fafafa;
    z-index: 29;
    pointer-events: none;
}

.sidebar-section-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--sidebar-text);
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--sidebar-text);
    font-size: 16px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    outline: none;
    padding: 0;
}

.sidebar-toggle:hover {
    background-color: var(--sidebar-item-hover);
}

.sidebar-toggle i {
    font-size: 18px;
    color: inherit;
    background: transparent;
    border: none;
    outline: none;
    display: block;
}

.sidebar.collapsed .sidebar-toggle {
    position: fixed;
    left: 10px;
    top: 14px;
    background-color: transparent;
    border: none;
    z-index: 1020;
    box-shadow: none;
    padding: 0;
    outline: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar.collapsed .sidebar-toggle i {
    color: var(--text-primary);
    background: transparent;
    border: none;
    display: block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    position: relative;
    z-index: 1010;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width) !important;
}

.sidebar.collapsed .sidebar-menu-text,
.sidebar.collapsed .sidebar-section-title,
.sidebar.collapsed .sidebar-conversations,
.sidebar.collapsed .sidebar-bottom,
.sidebar.collapsed .sidebar-brand-logo,
.sidebar.collapsed .sidebar-new-chat-btn {
    display: none;
}

.sidebar.collapsed .sidebar-menu-item {
    justify-content: center;
    padding: 12px;
    margin: 2px 4px;
}

.sidebar.collapsed .sidebar-menu-icon {
    margin-right: 0;
}

.sidebar-action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    height: 40px;
    margin-right: 5px;
}

.sidebar.collapsed .sidebar-action-buttons {
    display: none;
}

.sidebar.collapsed .sidebar-brand,
.sidebar.collapsed .sidebar-item {
    justify-content: center;
    padding: 12px 0;
}

.sidebar.collapsed .sidebar-item-icon {
    margin-right: 0;
}

.sidebar.collapsed .sidebar-top {
    display: flex;
    justify-content: center;
}

.sidebar.collapsed .new-chat-btn {
    width: 30px;
    height: 30px;
    padding: 0;
    justify-content: center;
    position: absolute;
    top: 60px;
    left: 10px;
    z-index: 1050;
    pointer-events: auto;
    border-radius: 50%;
    background-color: #000000;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.theme-dark .sidebar.collapsed .new-chat-btn {
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none;
}

.theme-dark .sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none;
} 