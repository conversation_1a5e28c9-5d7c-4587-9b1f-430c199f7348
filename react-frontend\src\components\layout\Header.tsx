import React, { useState } from 'react';
import { User } from '@/types';

interface HeaderProps {
  user: User | null;
  sidebarCollapsed: boolean;
  onNewChat: () => void;
  onOpenSettings: () => void;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({
  user,
  sidebarCollapsed,
  onNewChat,
  onOpenSettings,
  onLogout,
}) => {
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const handleUserMenuClick = () => {
    setShowUserDropdown(!showUserDropdown);
  };

  const handleSettingsClick = () => {
    setShowUserDropdown(false);
    onOpenSettings();
  };

  const handleLogoutClick = () => {
    setShowUserDropdown(false);
    onLogout();
  };

  return (
    <header className="chat-header">
      <div className="header-content">
        {/* Left section with new chat and brand */}
        <div className="header-left" id="headerLeft">
          {/* New chat button (only visible when sidebar is collapsed) */}
          {sidebarCollapsed && (
            <button
              id="headerNewChatBtn"
              className="header-new-chat-btn"
              title="New chat"
              onClick={onNewChat}
            >
              <img src="/new-chat-icon-larger.svg" alt="New Chat" className="new-chat-icon" />
            </button>
          )}
          {/* ZiaHR branding */}
          <div className="header-brand" id="headerBrand">
            <h1 className="brand-name">ZiaHR</h1>
          </div>
        </div>

        <div className="header-actions">
          {/* Dark mode toggle removed - only available in settings */}
          {/* User account button */}
          <div className="user-account-dropdown">
            <button
              id="userAccountBtn"
              className="header-action-btn login-icon"
              title="Account"
              onClick={handleUserMenuClick}
            >
              <i className="fas fa-user-circle"></i>
            </button>

            {showUserDropdown && (
              <div className="user-dropdown-menu" id="userDropdownMenu">
                <div className="user-info">
                  <div className="user-name">{user?.fullName || 'User'}</div>
                  <div className="user-email">{user?.email}</div>
                  {user?.employeeId && (
                    <div className="user-id">ID: {user.employeeId}</div>
                  )}
                </div>
                <div className="dropdown-divider"></div>
                <div className="dropdown-menu-items">
                  <button className="dropdown-item" onClick={handleSettingsClick}>
                    <i className="fas fa-cog"></i>
                    <span>Settings</span>
                  </button>
                  <button className="dropdown-item" onClick={handleLogoutClick}>
                    <i className="fas fa-sign-out-alt"></i>
                    <span>Log out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showUserDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserDropdown(false)}
        />
      )}
    </header>
  );
};

export default Header;
