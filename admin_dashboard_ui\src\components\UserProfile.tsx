import React from "react";
import { useAuthStore } from "../hooks/useAuthStore";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, Settings, LogOut, Shield } from "lucide-react";
import { useNavigate } from "react-router-dom";

export const UserProfile = () => {
  const { role, setRole, logout, user } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const getRoleColor = (userRole: string) => {
    return userRole === "superadmin" 
      ? "bg-blue-600 text-white" 
      : "bg-green-600 text-white";
  };

  const getRoleIcon = (userRole: string) => {
    return userRole === "superadmin" ? <Shield className="h-3 w-3" /> : <User className="h-3 w-3" />;
  };

  const getUserInitials = () => {
    if (user?.name) {
      return user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return role === "superadmin" ? "SA" : "V";
  };

  const getUserEmail = () => {
    return user?.email || (role === "superadmin" ? "<EMAIL>" : "<EMAIL>");
  };

  const getUserName = () => {
    return user?.name || (role === "superadmin" ? "Super Administrator" : "Viewer");
  };

  return (
    <div className="flex items-center gap-3">
      {/* Role Badge */}
      <div className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${getRoleColor(role)}`}>
        {getRoleIcon(role)}
        {role === "superadmin" ? "Superadmin" : "Viewer"}
      </div>

      {/* User Profile Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="relative">
            <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
              {getUserInitials()}
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {getUserName()}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {getUserEmail()}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Role Switcher */}
          <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
            Switch Role
          </DropdownMenuLabel>
          <DropdownMenuItem 
            onClick={() => setRole("superadmin")}
            className={role === "superadmin" ? "bg-accent" : ""}
          >
            <Shield className="mr-2 h-4 w-4" />
            <span>Superadmin</span>
            {role === "superadmin" && <span className="ml-auto text-xs">✓</span>}
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => setRole("viewer")}
            className={role === "viewer" ? "bg-accent" : ""}
          >
            <User className="mr-2 h-4 w-4" />
            <span>Viewer</span>
            {role === "viewer" && <span className="ml-auto text-xs">✓</span>}
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}; 