import { useState, useEffect, useCallback } from 'react';
import { jwtDecode } from 'jwt-decode';
import { Role } from '../types';

interface AuthState {
  user: string | null;
  role: Role | null;
  token: string | null;
}

export function useAuth() {
  return {
    user: '<EMAIL>',
    role: 'superadmin' as Role,
    token: 'mock-token',
    login: (_token?: string) => {},
    logout: () => {},
  };
} 