import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

interface TopIntentsBarChartProps {
  topIntents: Array<{ intent: string; count: number; samples?: string[] }>;
  loading?: boolean;
  error?: string | null;
}

const TopIntentsBarChart: React.FC<TopIntentsBarChartProps> = ({ topIntents, loading, error }) => {
  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!topIntents || !topIntents.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  return (
    <div className="h-80 w-full rounded-xl bg-white dark:bg-zinc-900">
      <ResponsiveContainer width="100%" height="100%">
        <Bar<PERSON>hart
          data={topIntents}
          layout="vertical"
          margin={{ top: 16, right: 16, left: 16, bottom: 0 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" />
          <XAxis type="number" stroke="hsl(var(--muted-foreground))" />
          <YAxis dataKey="intent" type="category" stroke="hsl(var(--muted-foreground))" width={140} />
          <Tooltip
            content={({ active, payload }) => {
              if (active && payload && payload.length) {
                const { intent, samples } = payload[0].payload;
                return (
                  <div className="bg-background dark:bg-zinc-900 border border-border dark:border-zinc-800 rounded shadow p-3 text-xs">
                    <div className="font-semibold mb-1">{intent}</div>
                    {samples && (
                      <>
                        <div>Sample queries:</div>
                        <ul className="list-disc ml-4">
                          {samples.map((q: string, i: number) => (
                            <li key={i}>{q}</li>
                          ))}
                        </ul>
                      </>
                    )}
                  </div>
                );
              }
              return null;
            }}
          />
          <Legend />
          <Bar dataKey="count" fill="var(--chart-accent)" name="Frequency" radius={[0, 4, 4, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TopIntentsBarChart; 