import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Chart, Line, PieChart, Pie, Cell, XAxis, YAxis, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from "recharts";
import { API_BASE_URL } from "../apiConfig";

const COLORS = ["#7c3aed", "#f59e42", "#10b981", "#f43f5e", "#6366f1", "#fbbf24", "#14b8a6", "#eab308"];

export function ChartsPage() {
  const [trendData, setTrendData] = useState<any[]>([]);
  const [typeData, setTypeData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    Promise.all([
      fetch(`${API_BASE_URL}/api/chat-trends`).then(res => res.json()),
      fetch(`${API_BASE_URL}/api/chat-types`).then(res => res.json())
    ]).then(([trends, types]) => {
      setTrendData(Array.isArray(trends) ? trends : []);
      setTypeData(Array.isArray(types) ? types : []);
    }).finally(() => setLoading(false));
  }, []);

  return (
    <div className="space-y-10">
      <div className="text-2xl font-bold text-accent-400 mb-4">Chat Analytics</div>
      {loading ? (
        <div className="text-neutral-400">Loading charts...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-neutral-900 rounded-xl p-6 shadow-lg border border-neutral-800">
            <div className="font-semibold mb-2">Chats Per Day (Bar Chart)</div>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={trendData} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
                <XAxis dataKey="date" stroke="#a3a3a3" />
                <YAxis stroke="#a3a3a3" />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#7c3aed" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="bg-neutral-900 rounded-xl p-6 shadow-lg border border-neutral-800">
            <div className="font-semibold mb-2">Chats Per Day (Line Chart)</div>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={trendData} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
                <XAxis dataKey="date" stroke="#a3a3a3" />
                <YAxis stroke="#a3a3a3" />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="count" stroke="#f59e42" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="bg-neutral-900 rounded-xl p-6 shadow-lg border border-neutral-800 md:col-span-2">
            <div className="font-semibold mb-2">Chat Types (Pie Chart)</div>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie data={Array.isArray(typeData) ? typeData : []} dataKey="count" nameKey="type" cx="50%" cy="50%" outerRadius={100} label>
                  {(Array.isArray(typeData) ? typeData : []).map((entry, idx) => (
                    <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
} 