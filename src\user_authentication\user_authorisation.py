import os
import bcrypt
import jwt
import pyotp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from ..utils.logger import get_logger
from ..database.user_db import UserModel, SessionModel
from passlib.context import CryptContext
import re

load_dotenv()
logger = get_logger(__name__)

# --- JWT Configuration ---
JWT_SECRET = os.getenv("JWT_SECRET")
if not JWT_SECRET:
    raise ValueError("JWT_SECRET environment variable is required for security.")

JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_HOURS = 24

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], bcrypt__rounds=12)

class TwoFAService:
    def __init__(self, email: str, secret: str, issuer: str):
        self.email = email
        self.secret = secret
        self.issuer = issuer
        self.totp = pyotp.TOTP(secret)

    def get_qr_url(self):
        return self.totp.provisioning_uri(name=self.email, issuer_name=self.issuer)

    def verify_code(self, code: str) -> bool:
        return self.totp.verify(code, valid_window=1)

class User2FAService(TwoFAService):
    def __init__(self, email: str, secret: str):
        super().__init__(email, secret, issuer="Ziantrix")

class Admin2FAService(TwoFAService):
    def __init__(self, email: str, secret: str):
        super().__init__(email, secret, issuer="Ziantrix Admin")

class AuthService:
    """Production-grade authentication service with 2FA support."""

    def __init__(self):
        self.user_model = UserModel()

    def hash_password(self, password: str) -> str:
        if not self.validate_password_strength(password):
            raise ValueError("Password does not meet security requirements.")
        try:
            return pwd_context.hash(password)
        except Exception as e:
            logger.exception("Error hashing password")
            raise

    def verify_password(self, stored_password: str, provided_password: str) -> bool:
        try:
            return pwd_context.verify(provided_password, stored_password)
        except Exception as e:
            logger.exception("Error verifying password")
            return False

    @staticmethod
    def validate_password_strength(password: str) -> bool:
        # Minimum 12 characters, mix of upper/lowercase, numbers, symbols
        if len(password) < 12:
            return False
        if not re.search(r"[A-Z]", password):
            return False
        if not re.search(r"[a-z]", password):
            return False
        if not re.search(r"[0-9]", password):
            return False
        if not re.search(r"[^A-Za-z0-9]", password):
            return False
        return True

    def generate_2fa_secret(self) -> str:
        return pyotp.random_base32()

    def get_2fa_service(self, user: dict, role: str = None):
        role = role or user.get('role', 'user')
        if role == 'admin':
            secret = user.get('two_fa_secret_admin')
            return Admin2FAService(user['email'], secret)
        else:
            secret = user.get('two_fa_secret_user')
            return User2FAService(user['email'], secret)

    def get_2fa_qr_url(self, email: str, secret: str, issuer: str = None, role: str = 'user') -> str:
        if role == 'admin':
            return Admin2FAService(email, secret).get_qr_url()
        else:
            return User2FAService(email, secret).get_qr_url()

    def verify_2fa_code(self, user: dict, code: str, role: str = None) -> bool:
        service = self.get_2fa_service(user, role)
        return service.verify_code(code)

    def register_user(self, email: str, password: str, full_name: str, employee_id: Optional[str] = None) -> Dict[str, Any]:
        try:
            existing_user = self.user_model.get_user_by_email(email)
            if existing_user:
                return {"success": False, "message": "User with this email already exists"}
            # Only require password for non-admin users
            if '@admin' in email or email.lower().endswith('admin'):  # Example logic, adjust as needed
                password_hash = None
            else:
                password_hash = self.hash_password(password)
            two_fa_secret = self.generate_2fa_secret()
            user_id = self.user_model.create_user(
                email=email,
                password_hash=password_hash,
                full_name=full_name,
                employee_id=employee_id,
                two_fa_secret_user=two_fa_secret if not (email and 'admin' in email.lower()) else None,
                two_fa_secret_admin=two_fa_secret if (email and 'admin' in email.lower()) else None,
                role='admin' if (email and 'admin' in email.lower()) else 'user'
            )
            if user_id:
                qr_url = self.get_2fa_qr_url(email, two_fa_secret, role='admin' if (email and 'admin' in email.lower()) else 'user')
                return {
                    "success": True,
                    "message": "User registered successfully",
                    "user_id": user_id,
                    "2fa_qr_url": qr_url
                }
            else:
                return {"success": False, "message": "Failed to register user"}
        except Exception as e:
            logger.exception("Error registering user")
            return {"success": False, "message": f"An error occurred: {str(e)}"}

    def login_user(self, email: str, password: str, two_fa_code: Optional[str] = None) -> Dict[str, Any]:
        user = self.user_model.get_user_by_email(email)
        if not user:
            return {"success": False, "message": "Invalid email or password"}
        # Only allow password login for non-admin users
        if user.get('role') == 'admin':
            return {"success": False, "message": "Admin users must use OTP login."}
        if not self.verify_password(user['password_hash'], password):
            return {"success": False, "message": "Invalid email or password"}

        # FIX: Check for two_fa_secret_user for normal users
        if user.get('two_fa_secret_user'):
            if not two_fa_code:
                return {"success": False, "message": "2FA code required"}
            if not self.verify_2fa_code(user, two_fa_code):
                return {"success": False, "message": "Invalid 2FA code"}

        self.user_model.update_last_login(user['id'])
        token = self.generate_token(user)

        # --- Create session record for chatbot user ---
        try:
            import uuid
            session_id = str(uuid.uuid4())
            # These would be passed in or extracted from request context in a real app
            ip_address = None
            user_agent = None
            browser = None
            os = None
            device_fingerprint = None
            login_time = datetime.utcnow().isoformat()
            last_activity = login_time
            location_country = None
            location_city = None
            latitude = None
            longitude = None
            auth_method = 'password'
            SessionModel().create_session(
                id=session_id,
                user_id=user['id'],
                ip_address=ip_address,
                user_agent=user_agent,
                browser=browser,
                os=os,
                device_fingerprint=device_fingerprint,
                login_time=login_time,
                last_activity=last_activity,
                location_country=location_country,
                location_city=location_city,
                latitude=latitude,
                longitude=longitude,
                auth_method=auth_method,
                success=True,
                user_type="chatbot_user"
            )
        except Exception as e:
            logger.exception("Failed to create chatbot user session record")

        return {
            "success": True,
            "message": "Login successful",
            "token": token,
            "user": {
                "id": user['id'],
                "email": user['email'],
                "full_name": user['full_name'],
                "employee_id": user.get('employee_id')
            }
        }

    def generate_token(self, user: Dict[str, Any]) -> str:
        payload = {
            'user_id': user['id'],
            'email': user['email'],
            'exp': datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
        }
        try:
            return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
        except Exception as e:
            logger.exception("Error generating JWT token")
            raise

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
            user_id = payload.get('user_id')
            if user_id:
                return self.user_model.get_user_by_id(user_id)
            return None
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
            return None
        except Exception as e:
            logger.exception("Error verifying JWT token")
            return None