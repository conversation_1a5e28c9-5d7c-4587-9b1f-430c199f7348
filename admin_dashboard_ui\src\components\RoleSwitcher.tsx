import React from "react";
import { useAuthStore } from "../hooks/useAuthStore";
import { Button } from "@/components/ui/button";

export const RoleSwitcher = () => {
  const { role, setRole } = useAuthStore();
  return (
    <div className="flex items-center gap-2">
      <span className="text-xs text-muted-foreground">Role:</span>
      <Button
        variant={role === "superadmin" ? "default" : "outline"}
        size="sm"
        onClick={() => setRole("superadmin")}
        aria-pressed={role === "superadmin"}
      >
        Superadmin
      </Button>
      <Button
        variant={role === "viewer" ? "default" : "outline"}
        size="sm"
        onClick={() => setRole("viewer")}
        aria-pressed={role === "viewer"}
      >
        Viewer
      </Button>
    </div>
  );
}; 