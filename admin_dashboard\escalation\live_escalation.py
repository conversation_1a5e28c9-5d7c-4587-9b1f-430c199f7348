import requests
import datetime

SLACK_WEBHOOK_URL = 'https://hooks.slack.com/services/your/webhook/url'  # Replace with real URL

def escalate_to_slack(query, user_id):
    timestamp = datetime.datetime.utcnow().isoformat()
    message = {
        'text': f"[Escalation] Unhandled query from {user_id} at {timestamp}:\n{query}"
    }
    response = requests.post(SLACK_WEBHOOK_URL, json=message)
    return response.status_code == 200

# For future: Add WhatsApp Business API integration, error handling, etc. 