# QA Checklist

## Analytics → Overview
- [ ] All charts and tables render with real/mock data
- [ ] Responsive layout on mobile, tablet, desktop
- [ ] Loading skeletons show while fetching data
- [ ] Error and empty states are clear and styled
- [ ] Keyboard navigation: Tab through all filters, charts, export buttons
- [ ] ARIA labels on charts, tables, and export controls
- [ ] RBAC: Only allowed roles can view/export data
- [ ] API errors handled gracefully (no crashes)

## NER + Intent Trainer
- [ ] Table/inline view renders with data and pagination
- [ ] Entity tags and intent dropdowns are editable
- [ ] Save/Flag actions work and show feedback
- [ ] Filters and search update results as expected
- [ ] Loading, error, and empty states are clear
- [ ] Keyboard navigation: Tab through all fields, dropdowns, and actions
- [ ] ARIA labels on entity tags, dropdowns, and action buttons
- [ ] RBAC: Only allowed roles can edit/train
- [ ] API errors handled gracefully

## Live Handover Interface
- [ ] Tabs switch between Queue and In-Progress views
- [ ] Accept/Decline/Close/Transfer actions work (mocked or real)
- [ ] Chat preview stub displays for in-progress sessions
- [ ] Loading, error, and empty states are clear
- [ ] Keyboard navigation: Tab through all queue/session actions
- [ ] ARIA labels on all action buttons and chat preview
- [ ] RBAC: Only allowed roles can accept/close/transfer
- [ ] API errors handled gracefully 