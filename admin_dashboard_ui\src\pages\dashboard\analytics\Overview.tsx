import React, { Suspense, useState } from "react";
import use<PERSON><PERSON> from "swr";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ExportButton } from "@/components/ExportButton";
import { LiveIndicator } from "@/components/LiveIndicator";
import { GlobalDateFilter } from "@/components/GlobalDateFilter";
import { TrendingUp, Users, MessageSquare, Clock } from "lucide-react";

const ChatsPerDayChart = React.lazy(() => import("./components/ChatsPerDayChart"));
const TopIntentsChart = React.lazy(() => import("./components/TopIntentsChart"));
const TopicTrendsChart = React.lazy(() => import("./components/TopicTrendsChart"));
const SentimentPieChart = React.lazy(() => import("./components/SentimentPieChart"));
const TopQuestionsTable = React.lazy(() => import("./components/TopQuestionsTable"));

const fetcher = (url: string) => fetch(url).then(res => res.json());

const KPI_CARDS = [
  { key: "total_queries", label: "Total Queries", icon: MessageSquare, description: "vs last week" },
  { key: "active_users", label: "Active Users", icon: Users, description: "vs last week" },
  { key: "avg_sentiment", label: "Avg. Sentiment", icon: TrendingUp, description: "vs last week" },
  { key: "unique_questions", label: "Unique Questions", icon: Clock, description: "vs last week" },
];

const Overview = () => {
  const [dateRange, setDateRange] = useState("7d");
  const { data, error, isLoading } = useSWR("/api/chat-analytics/live", fetcher, { refreshInterval: 30000 });

  const handleExport = (type: string) => {
    console.log(`Exporting overview as ${type}`);
    // Add actual export logic here
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Overview</h1>
          <p className="text-muted-foreground">
            Monitor your chatbot performance and user engagement metrics
          </p>
        </div>
        <div className="flex items-center gap-3">
          <GlobalDateFilter
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
          />
          <ExportButton
            onExportPDF={() => handleExport("pdf")}
            onExportCSV={() => handleExport("csv")}
            onExportPNG={() => handleExport("png")}
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full rounded-2xl" />
          ))
        ) : error ? (
          <div className="col-span-4 text-red-500 p-4">{error}</div>
        ) : (
          KPI_CARDS.map((stat) => {
            const IconComponent = stat.icon;
            return (
              <Card key={stat.key} className="rounded-2xl border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {stat.label}
                      </p>
                      <p className="text-2xl font-bold mt-1">{data?.[stat.key] ?? "-"}</p>
                      <div className="flex items-center gap-1 mt-2">
                        <span className="text-xs text-muted-foreground">
                          {stat.description}
                        </span>
                      </div>
                    </div>
                    <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chats Per Day */}
        <Card className="rounded-2xl border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
          <CardHeader className="flex flex-row items-center justify-between pb-4">
            <div>
              <CardTitle className="text-xl font-semibold">Chats Per Day</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <LiveIndicator />
                <span className="text-sm text-muted-foreground">Real-time data</span>
              </div>
            </div>
            <ExportButton
              onExportPDF={() => handleExport("pdf")}
              onExportCSV={() => handleExport("csv")}
              onExportPNG={() => handleExport("png")}
              size="sm"
            />
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <Suspense fallback={<Skeleton className="h-64 w-full rounded-lg" />}>
              <ChatsPerDayChart dateRange={dateRange} />
            </Suspense>
          </CardContent>
        </Card>
        {/* Add more charts as needed, passing data from /api/chat-analytics/live if required */}
      </div>
    </div>
  );
};

export default Overview;