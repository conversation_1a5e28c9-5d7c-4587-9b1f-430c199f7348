import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import api from "@/services/api";

const TABS = ["Queue", "In-Progress"];

const LiveHandover = () => {
  const [tab, setTab] = useState("Queue");
  const [queue, setQueue] = useState<any[]>([]);
  const [sessions, setSessions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch pending escalations for Queue tab
  useEffect(() => {
    if (tab === "Queue") {
      setLoading(true);
      setError(null);
      api
        .get("/escalations/pending")
        .then((res) => setQueue(res.data || []))
        .catch((err) => setError("Failed to load queue"))
        .finally(() => setLoading(false));
    }
  }, [tab]);

  // Fetch in-progress sessions for In-Progress tab (mock for now)
  useEffect(() => {
    if (tab === "In-Progress") {
      setLoading(true);
      setError(null);
      // Mock API for in-progress sessions
      setTimeout(() => {
        setSessions([
          {
            id: "sess1",
            user: "<EMAIL>",
            topic: "Leave Balance",
            started: "2024-06-12 10:15",
            chat: [
              { sender: "user", text: "I need help with my leave balance." },
              { sender: "admin", text: "Sure, let me check that for you." },
            ],
          },
        ]);
        setLoading(false);
      }, 800);
    }
  }, [tab]);

  // Accept/Decline handlers (mock)
  const handleAccept = (id: string) => {
    alert(`Accepted escalation ${id} (mock)`);
    // Move to sessions (mock)
    setSessions((prev) => [
      ...prev,
      { ...queue.find((q) => q.id === id), started: new Date().toISOString(), chat: [] },
    ]);
    setQueue((prev) => prev.filter((q) => q.id !== id));
  };
  const handleDecline = (id: string) => {
    alert(`Declined escalation ${id} (mock)`);
    setQueue((prev) => prev.filter((q) => q.id !== id));
  };
  const handleClose = (id: string) => {
    alert(`Closed session ${id} (mock)`);
    setSessions((prev) => prev.filter((s) => s.id !== id));
  };
  const handleTransfer = (id: string) => {
    alert(`Transferred session ${id} (mock)`);
  };

  return (
    <div className="max-w-6xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Live Handover Interface</CardTitle>
          <div className="flex gap-2 mt-4">
            {TABS.map((t) => (
              <Button
                key={t}
                variant={tab === t ? "default" : "outline"}
                size="sm"
                onClick={() => setTab(t)}
              >
                {t}
              </Button>
            ))}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-80 w-full" />
          ) : error ? (
            <div className="text-red-500 p-4">{error}</div>
          ) : tab === "Queue" ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
                <thead className="bg-muted dark:bg-zinc-800">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">User</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Timestamp</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Topic</th>
                    <th className="px-4 py-2"></th>
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-zinc-900">
                  {queue.map((row, idx) => (
                    <tr key={row.id || idx} className="border-b border-border dark:border-zinc-800">
                      <td className="px-4 py-2 text-sm">{row.user}</td>
                      <td className="px-4 py-2 text-sm">{row.timestamp}</td>
                      <td className="px-4 py-2 text-sm">{row.topic}</td>
                      <td className="px-4 py-2 text-sm flex gap-2">
                        <Button size="sm" variant="default" onClick={() => handleAccept(row.id)}>
                          Accept
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDecline(row.id)}>
                          Decline
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
                <thead className="bg-muted dark:bg-zinc-800">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">User</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Topic</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Started</th>
                    <th className="px-4 py-2"></th>
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-zinc-900">
                  {sessions.map((row, idx) => (
                    <tr key={row.id || idx} className="border-b border-border dark:border-zinc-800">
                      <td className="px-4 py-2 text-sm">{row.user}</td>
                      <td className="px-4 py-2 text-sm">{row.topic}</td>
                      <td className="px-4 py-2 text-sm">{row.started}</td>
                      <td className="px-4 py-2 text-sm flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => handleClose(row.id)}>
                          Close
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleTransfer(row.id)}>
                          Transfer
                        </Button>
                      </td>
                    </tr>
                    // Chat preview stub
                  ))}
                </tbody>
              </table>
              {/* Chat preview stub for first session */}
              {sessions.length > 0 && (
                <div className="mt-6 p-4 border rounded bg-muted dark:bg-zinc-800">
                  <div className="font-semibold mb-2">Chat Preview (stub)</div>
                  <div className="space-y-2">
                    {sessions[0].chat?.map((msg: any, i: number) => (
                      <div key={i} className={msg.sender === "admin" ? "text-right" : "text-left"}>
                        <span className={msg.sender === "admin" ? "bg-primary/20" : "bg-muted"}>
                          <span className="inline-block px-2 py-1 rounded">
                            <span className="font-semibold">{msg.sender === "admin" ? "Admin" : "User"}:</span> {msg.text}
                          </span>
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LiveHandover; 