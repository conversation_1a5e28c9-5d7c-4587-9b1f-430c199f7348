# Final QA Pass Checklist

## Accessibility
- [ ] All interactive elements are keyboard accessible (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>)
- [ ] Modals and alerts trap focus and return focus on close
- [ ] ARIA labels/roles are present on custom components (modals, toggles, dropdowns, tables)
- [ ] Sufficient color contrast in both light and dark mode
- [ ] Screen reader navigation tested for all major flows

## RBAC Logic
- [ ] Sidebar and routes are correctly gated by user role
- [ ] Role switcher updates visible modules in real time
- [ ] Unauthorized access to restricted modules is blocked
- [ ] All role-based actions (edit, export, retrain, etc.) are gated

## API Fallback/Error States
- [ ] All API-driven components show skeletons while loading
- [ ] Error messages are clear, actionable, and styled
- [ ] Empty states are present and styled for all tables/charts
- [ ] No unhandled promise rejections or crashes on API failure
- [ ] Environment-based API config works for dev/staging/prod 