import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Mic,
  AlertCircle,
  X,
  FileText,
  Smile
} from 'lucide-react';
import { FileAttachment } from '@/types';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn, formatFileSize } from '@/lib/utils';
import { useHotkeys } from 'react-hotkeys-hook';

interface ChatInputProps {
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  attachedFiles: FileAttachment[];
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  isLoading?: boolean;
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  attachedFiles,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  isLoading = false,
  placeholder = "Ask anything about leaves, benefits, or company policies...",
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message.trim(), attachedFiles);
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      const fileAttachment: FileAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      };
      onAddFile(fileAttachment);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = message.trim().length > 0 && !isLoading;

  const containerVariants = {
    focused: {
      boxShadow: "0 0 0 2px hsl(var(--ring))",
      transition: { duration: 0.2 }
    },
    unfocused: {
      boxShadow: "0 0 0 0px hsl(var(--ring))",
      transition: { duration: 0.2 }
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      {/* File Attachments */}
      <AnimatePresence>
        {attachedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 flex flex-wrap gap-2"
          >
            {attachedFiles.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 rounded-lg bg-muted px-3 py-2 text-sm"
              >
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{file.name}</span>
                <span className="text-muted-foreground">
                  ({formatFileSize(file.size)})
                </span>
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={() => onRemoveFile(file.id)}
                  className="h-5 w-5 hover:bg-destructive hover:text-destructive-foreground"
                >
                  <X className="h-3 w-3" />
                </Button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Input Container */}
      <motion.div
        variants={containerVariants}
        animate={isFocused ? "focused" : "unfocused"}
        className={cn(
          "chat-input-container",
          isLoading && "opacity-50 cursor-not-allowed"
        )}
      >
        <form onSubmit={handleSubmit} className="flex items-end gap-2">
          {/* Main Input Area */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onCompositionStart={() => setIsComposing(true)}
              onCompositionEnd={() => setIsComposing(false)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              disabled={isLoading}
              autoResize
              className="chat-input min-h-[44px] pr-12 resize-none border-0 bg-transparent focus-visible:ring-0"
            />

            {/* Emoji Button (placeholder for future enhancement) */}
            <Button
              type="button"
              variant="ghost"
              size="icon-sm"
              className="absolute right-2 bottom-2 h-8 w-8 text-muted-foreground hover:text-foreground"
              disabled={isLoading}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            {/* File Upload */}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
              className="text-muted-foreground hover:text-foreground"
              title="Attach file (Ctrl+K)"
            >
              <Paperclip className="h-4 w-4" />
            </Button>

            {/* Voice Input */}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={onOpenVoice}
              disabled={isLoading}
              className="text-muted-foreground hover:text-foreground"
              title="Voice input"
            >
              <Mic className="h-4 w-4" />
            </Button>

            {/* Escalation */}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={onOpenEscalation}
              disabled={isLoading}
              className="text-muted-foreground hover:text-destructive"
              title="Escalate to HR"
            >
              <AlertCircle className="h-4 w-4" />
            </Button>

            {/* Send Button */}
            <AnimatePresence>
              {canSend && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    type="submit"
                    size="icon"
                    disabled={isLoading}
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                    title="Send message (Enter)"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </form>

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileUpload}
          accept=".pdf,.docx,.txt,.md"
          multiple
          hidden
        />
      </motion.div>

      {/* Keyboard Shortcuts Hint */}
      {isFocused && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="mt-2 text-xs text-muted-foreground text-center"
        >
          Press <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd> to send,
          <kbd className="px-1 py-0.5 bg-muted rounded text-xs ml-1">Shift + Enter</kbd> for new line
        </motion.div>
      )}
    </div>
  );
};

export default ChatInput;
