import { useQuery } from 'react-query';
import { getStatsUsers, getStatsUsage, getStatsIntents } from "@/services/api";
import { Layout } from '../../components/Layout';
import { ProtectedRoute } from '../../components/ProtectedRoute';
import {
  LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, BarChart, Bar, PieChart, Pie, Cell, Legend
} from 'recharts';

const COLORS = ['#6366f1', '#10b981', '#f59e42', '#ef4444', '#a21caf', '#0ea5e9', '#fbbf24', '#14b8a6'];

export default function AnalyticsPage() {
  const usersQuery = useQuery(['statsUsers'], async () => (await getStatsUsers()).data);
  const usageQuery = useQuery(['statsUsage'], async () => (await getStatsUsage()).data);
  const intentsQuery = useQuery(['statsIntents'], async () => (await getStatsIntents()).data);

  // Mock transform for demo: adapt to your backend's actual data shape
  const usersData = usersQuery.data?.users_over_time || [];
  const usageData = usageQuery.data?.usage || [];
  const intentsData = intentsQuery.data?.intents || [];

  return (
    <ProtectedRoute roles={['superadmin', 'admin', 'hr_lead']}>
      <Layout />
    </ProtectedRoute>
  );
} 