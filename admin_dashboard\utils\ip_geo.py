import httpx

async def get_geo_from_ip(ip: str) -> dict:
    try:
        async with httpx.AsyncClient() as client:
            r = await client.get(f"https://ipapi.co/{ip}/json/")
            if r.status_code == 200:
                d = r.json()
                return {
                    "country": d.get("country_name"),
                    "region": d.get("region"),
                    "city": d.get("city")
                }
    except Exception:
        pass
    return {"country": None, "region": None, "city": None} 