import React from 'react';
import { Message, FileAttachment } from '@/types';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import { cn } from '@/lib/utils';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  className?: string;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  className,
}) => {

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Messages Area */}
      <ChatMessages
        messages={messages}
        isLoading={isLoading}
        onSuggestionClick={onSendMessage}
        showWelcome={messages.length === 0}
      />

      {/* Input Area */}
      <div className="border-t bg-background/95 backdrop-blur-sm">
        <ChatInput
          onSendMessage={onSendMessage}
          attachedFiles={attachedFiles}
          onAddFile={onAddFile}
          onRemoveFile={onRemoveFile}
          onOpenVoice={onOpenVoice}
          onOpenEscalation={onOpenEscalation}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default ChatContainer;
