import React, { Suspense, lazy, useEffect } from "react";
import { <PERSON>rows<PERSON><PERSON>outer, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout";
import { useThemeStore } from "@/hooks/useThemeStore";
import LoginPage from "./pages/login/index";
import { PermissionsProvider } from "./context/PermissionsProvider";

// Lazy load all sidebar route pages
const AnalyticsOverview = lazy(() => import("./pages/dashboard/analytics/Overview"));
const AnalyticsInsights = lazy(() => import("./pages/dashboard/analytics/Insights"));
const ChatLogs = lazy(() => import("./pages/dashboard/analytics/ChatLogs"));

const FeedbackTrends = lazy(() => import("./pages/dashboard/feedback/Trends"));
const FeedbackEscalated = lazy(() => import("./pages/dashboard/feedback/Escalated"));
const FeedbackResolution = lazy(() => import("./pages/dashboard/feedback/Resolution"));

const AIWeeklyDigest = lazy(() => import("./pages/dashboard/ai/WeeklyDigest"));
const AIPolicyDrift = lazy(() => import("./pages/dashboard/ai/PolicyDrift"));

const TrainingMisunderstood = lazy(() => import("./pages/dashboard/training/Misunderstood"));
const TrainingNERIntent = lazy(() => import("./pages/dashboard/training/NERIntent"));

const LiveQueue = lazy(() => import("./pages/dashboard/live/Queue"));
const LiveOngoing = lazy(() => import("./pages/dashboard/live/Ongoing"));

const ComplianceGDPR = lazy(() => import("./pages/dashboard/compliance/GDPR"));
const ComplianceDeletion = lazy(() => import("./pages/dashboard/compliance/Deletion"));
const ComplianceSensitive = lazy(() => import("./pages/dashboard/compliance/Sensitive"));

const UsersAdmins = lazy(() => import("./pages/dashboard/users/Admins"));
const UsersRoles = lazy(() => import("./pages/dashboard/users/Roles"));
const AdminUsersRoles = lazy(() => import("./components/AdminUsersRoles"));

const SettingsTheme = lazy(() => import("./pages/dashboard/settings/Theme"));
const SettingsEmail = lazy(() => import("./pages/dashboard/settings/Email"));
const DeviceIntelligence = lazy(() => import("./pages/dashboard/DeviceIntelligence"));

// Add ErrorBoundary component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean; error: any }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  static getDerivedStateFromError(error: any) {
    return { hasError: true, error };
  }
  componentDidCatch(error: any, errorInfo: any) {
    // You can log error here if needed
    // console.error(error, errorInfo);
  }
  render() {
    if (this.state.hasError) {
      return (
        <div style={{ color: 'red', padding: 32 }}>
          <h1>Something went wrong.</h1>
          <pre>{this.state.error?.toString()}</pre>
        </div>
      );
    }
    return this.props.children;
  }
}

export default function App() {
  // Ensure theme is applied on initial load
  const { theme } = useThemeStore();
  useEffect(() => {
    const root = document.documentElement;
    if (theme === "dark") {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }
  }, [theme]);

  return (
    <BrowserRouter>
      <ErrorBoundary>
        <PermissionsProvider>
          <Suspense fallback={<div className="p-8 text-center">Loading...</div>}>
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/" element={<Layout />}>
                <Route index element={<AnalyticsOverview />} />
                {/* Analytics */}
                <Route path="analytics/overview" element={<AnalyticsOverview />} />
                <Route path="analytics/insights" element={<AnalyticsInsights />} />
                <Route path="/chat_logs" element={<ChatLogs />} />
                {/* Feedback & Escalations */}
                <Route path="feedback/trends" element={<FeedbackTrends />} />
                <Route path="feedback/escalated" element={<FeedbackEscalated />} />
                <Route path="feedback/resolution" element={<FeedbackResolution />} />
                {/* AI Insights */}
                <Route path="ai/weekly-digest" element={<AIWeeklyDigest />} />
                <Route path="ai/policy-drift" element={<AIPolicyDrift />} />
                {/* Training Tools */}
                <Route path="training/misunderstood" element={<TrainingMisunderstood />} />
                <Route path="training/ner-intent" element={<TrainingNERIntent />} />
                {/* Live Support */}
                <Route path="live-support/queue" element={<LiveQueue />} />
                <Route path="live-support/ongoing" element={<LiveOngoing />} />
                {/* Compliance & Auditing */}
                <Route path="compliance/gdpr" element={<ComplianceGDPR />} />
                <Route path="compliance/deletion" element={<ComplianceDeletion />} />
                <Route path="compliance/sensitive" element={<ComplianceSensitive />} />
                {/* Users */}
                <Route path="admin-users-roles" element={<AdminUsersRoles />} />
                {/* Settings */}
                <Route path="settings/theme" element={<SettingsTheme />} />
                <Route path="settings/email" element={<SettingsEmail />} />
                {/* Device Intelligence */}
                <Route path="device-intelligence" element={<DeviceIntelligence />} />
              </Route>
            </Routes>
          </Suspense>
        </PermissionsProvider>
      </ErrorBoundary>
    </BrowserRouter>
  );
}
