import React, { useState } from 'react';
import { User, ThemeMode } from '@/types';

interface SettingsModalProps {
  onClose: () => void;
  theme: ThemeMode['mode'];
  onThemeChange: (theme: ThemeMode['mode']) => void;
  user: User | null;
  onUpdateUser: (updates: Partial<User>) => void;
  onLogout: () => void;
  onClearAllChats: () => void;
  onOpenArchivedChats: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  onClose,
  theme,
  onThemeChange,
  user,
  onUpdateUser,
  onLogout,
  onClearAllChats,
  onOpenArchivedChats,
}) => {
  const [activeTab, setActiveTab] = useState<'general' | 'security' | 'personal'>('general');
  const [personalInfo, setPersonalInfo] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
    employeeId: user?.employeeId || '',
  });
  const [isSaving, setIsSaving] = useState(false);

  const handlePersonalInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPersonalInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleSavePersonalInfo = async () => {
    setIsSaving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      onUpdateUser(personalInfo);
    } finally {
      setIsSaving(false);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const confirmAction = (action: () => void, message: string) => {
    if (window.confirm(message)) {
      action();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content max-w-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-primary-border dark:border-dark-border">
          <h3 className="text-lg font-semibold text-primary-text dark:text-dark-text">
            Settings
          </h3>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-primary-text-secondary dark:text-dark-text-secondary"></i>
          </button>
        </div>

        {/* Body */}
        <div className="flex">
          {/* Sidebar */}
          <div className="w-48 border-r border-primary-border dark:border-dark-border">
            <nav className="p-4 space-y-2">
              {[
                { id: 'general', icon: 'fas fa-cog', label: 'General' },
                { id: 'security', icon: 'fas fa-shield-alt', label: 'Security' },
                { id: 'personal', icon: 'fas fa-user', label: 'Personal' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors duration-200
                    ${activeTab === tab.id
                      ? 'bg-primary-accent dark:bg-dark-accent text-white'
                      : 'text-primary-text dark:text-dark-text hover:bg-primary-secondary dark:hover:bg-dark-secondary'
                    }
                  `}
                >
                  <i className={tab.icon}></i>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6">
            {/* General Tab */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                {/* Theme */}
                <div className="flex items-center justify-between">
                  <span className="text-primary-text dark:text-dark-text">Theme</span>
                  <select
                    value={theme}
                    onChange={(e) => onThemeChange(e.target.value as ThemeMode['mode'])}
                    className="form-input w-32"
                  >
                    <option value="system">System</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>

                {/* Language */}
                <div className="flex items-center justify-between">
                  <span className="text-primary-text dark:text-dark-text">Language</span>
                  <select className="form-input w-32">
                    <option value="auto-detect">Auto-detect</option>
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>

                {/* Archived chats */}
                <div className="flex items-center justify-between">
                  <span className="text-primary-text dark:text-dark-text">Archived chats</span>
                  <button onClick={onOpenArchivedChats} className="btn-secondary">
                    Manage
                  </button>
                </div>

                {/* Clear all chats */}
                <div className="flex items-center justify-between">
                  <span className="text-primary-text dark:text-dark-text">Delete all chats</span>
                  <button
                    onClick={() => confirmAction(onClearAllChats, 'Are you sure you want to delete all chats? This action cannot be undone.')}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200"
                  >
                    Delete all
                  </button>
                </div>

                {/* Logout */}
                <div className="flex items-center justify-between">
                  <span className="text-primary-text dark:text-dark-text">Log out on this device</span>
                  <button onClick={onLogout} className="btn-secondary">
                    Log out
                  </button>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-primary-text dark:text-dark-text">Multi-factor authentication</div>
                    <p className="text-sm text-primary-text-secondary dark:text-dark-text-secondary mt-1">
                      Require an extra security challenge when logging in.
                    </p>
                  </div>
                  <button className="btn-secondary">Enable</button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-primary-text dark:text-dark-text">Log out of all devices</div>
                    <p className="text-sm text-primary-text-secondary dark:text-dark-text-secondary mt-1">
                      Log out of all active sessions across all devices.
                    </p>
                  </div>
                  <button className="btn-secondary">Log out all</button>
                </div>
              </div>
            )}

            {/* Personal Tab */}
            {activeTab === 'personal' && (
              <div className="space-y-6">
                <h4 className="text-lg font-medium text-primary-text dark:text-dark-text">
                  Personal Information
                </h4>
                
                <div className="space-y-4">
                  <div className="form-group">
                    <label htmlFor="personalFullName" className="form-label">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="personalFullName"
                      name="fullName"
                      value={personalInfo.fullName}
                      onChange={handlePersonalInfoChange}
                      className="form-input"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="personalEmail" className="form-label">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="personalEmail"
                      name="email"
                      value={personalInfo.email}
                      onChange={handlePersonalInfoChange}
                      className="form-input"
                      placeholder="Enter your email"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="personalEmployeeId" className="form-label">
                      Employee ID
                    </label>
                    <input
                      type="text"
                      id="personalEmployeeId"
                      name="employeeId"
                      value={personalInfo.employeeId}
                      onChange={handlePersonalInfoChange}
                      className="form-input"
                      placeholder="Enter your employee ID"
                    />
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={handleSavePersonalInfo}
                      disabled={isSaving}
                      className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSaving ? (
                        <div className="flex items-center space-x-2">
                          <div className="loading-spinner"></div>
                          <span>Saving...</span>
                        </div>
                      ) : (
                        'Save Changes'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
