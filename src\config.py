"""
Configuration settings for the Advanced RAG Chatbot.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Base directories
BASE_DIR = Path(__file__).resolve().parent.parent
DATA_DIR = BASE_DIR / "data"
PROCESSED_DIR = DATA_DIR / "processed"
RAW_DIR = DATA_DIR / "raw_files"
DB_DIR = DATA_DIR / "db"
MODELS_DIR = DATA_DIR / "models"
DOCUMENT_DB_PATH= DB_DIR /"documents.db"
CONVERSATION_DB_PATH= DB_DIR /"convo.db"
USER_DB_PATH= DB_DIR /"users.db"
ADMIN_USERS_DB_PATH = Path("admin_dashboard/admin_users.db")
TRAINING_DATA_PATH = DATA_DIR / "training" / "intent_training_data.jsonl"
CALIBRATION_PATH = DATA_DIR /"CALIBRATION" / "intent_calibration_stats.json"


# Create directories if they don't exist
for directory in [DATA_DIR, PROCESSED_DIR, RAW_DIR, DB_DIR, MODELS_DIR]:
    directory.mkdir(exist_ok=True, parents=True)

# Database settings
DATABASE_PATH = DB_DIR / "chatbot.db"
SIMILARITY_THRESHOLD = 0.4
MAX_VECTOR_SEARCH_TOP_K = 20

# ======== OPTIMIZED MODEL SETTINGS ========
# Use single model for multiple purposes to reduce loading time
UNIFIED_MODEL_NAME = EMBEDDING_MODEL_NAME = INTENT_MODEL_NAME = "sentence-transformers/multi-qa-mpnet-base-dot-v1"

# Fallback to larger model if needed (configurable)
USE_LARGE_MODEL = os.getenv("USE_LARGE_MODEL", "false").lower() == "true"
if USE_LARGE_MODEL:
    EMBEDDING_MODEL_NAME = "sentence-transformers/multi-qa-mpnet-base-dot-v1"


# LLM Parameters (for ChatGroq)
LLM_DEFAULT_TEMPERATURE = 0.7  # Controls randomness. Lower is more deterministic.
LLM_TOP_P = 1.0                # Nucleus sampling. 1.0 means all tokens are considered.
LLM_FREQUENCY_PENALTY = 0.0    # Penalizes new tokens based on their existing frequency in the text so far.
LLM_PRESENCE_PENALTY = 0.0     # Penalizes new tokens based on whether they appear in the text so far.
LLM_MODEL_NAME = os.getenv("GROQ_MODEL", "llama3-8b-8192")

# ======== PERFORMANCE OPTIMIZATION SETTINGS ========
# Lazy loading configuration
LAZY_LOADING = {
    'ner_model': os.getenv("LAZY_LOAD_NER", "true").lower() == "true",
    'intent_classifier': os.getenv("LAZY_LOAD_INTENT", "true").lower() == "true",
    'email_service': os.getenv("LAZY_LOAD_EMAIL", "true").lower() == "true"
}

# Model sharing between services
ENABLE_MODEL_SHARING = True


# Text-to-Speech Model Configuration
TTS_DEVICE = "cpu"
TTS_MODEL_NAME = "myshell-ai/MeloTTS"
AUDIO_PLAYBACK_RATE = 44100

# Async loading settings
ENABLE_ASYNC_LOADING = os.getenv("ENABLE_ASYNC_LOADING", "true").lower() == "true"
MAX_CONCURRENT_MODEL_LOADS = 3

# ======== VECTOR SEARCH SETTINGS ========
VECTOR_DIMENSION = 768
SIMILARITY_THRESHOLD = 0.6
MAX_CONTEXT_DOCUMENTS = 5

# Context Builder Settings
CONTEXT_BUILDER_MAX_RETRIES = 3

# Conversation settings
MAX_HISTORY_MESSAGES = 30

# WHISPER SPEECH TO TEXT SETTINGS
WHISPER_MODEL_NAME = "medium" # Corrected
WHISPER_MODEL_DIR = Path(os.getenv("WHISPER_MODEL_CACHE_DIR", "./whisper_models_cache"))
SPEECH_RECOGNITION_DURATION = 10
SPEECH_SAMPLE_RATE = 16000

# ======== API KEYS ========
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GROQ_API_KEY:
    raise ValueError("❌ GROQ_API_KEY is not set in the environment or .env file.")


# ======== ML MODEL SETTINGS ========
INTENT_CLASSIFIER_CONFIDENCE_THRESHOLD = 0.6
NER_CONFIDENCE_THRESHOLD = 0.7

# ======== RESPONSE SETTINGS ========
DEFAULT_RESPONSE_MODE = os.getenv("DEFAULT_RESPONSE_MODE", "concise")  # concise, detailed, auto
MAX_RESPONSE_WORDS = int(os.getenv("MAX_RESPONSE_WORDS", "500"))
ENABLE_AUTO_CONCISENESS = os.getenv("ENABLE_AUTO_CONCISENESS", "true").lower() == "true"


# ======== STARTUP OPTIMIZATION ========
# Skip heavy operations on startup
PROCESS_HR_FILES_ON_STARTUP = os.getenv("PROCESS_HR_FILES", "false").lower() == "true"
PRELOAD_MODELS_ON_STARTUP = os.getenv("PRELOAD_MODELS", "false").lower() == "true"
VALIDATE_MODELS_ON_STARTUP = os.getenv("VALIDATE_MODELS", "false").lower() == "true"

# Background processing
ENABLE_BACKGROUND_PROCESSING = os.getenv("ENABLE_BACKGROUND_PROCESSING", "true").lower() == "true"

# ======== DOCUMENT SETTINGS ========
MAX_DOCUMENT_VERSIONS = 5
DOCUMENT_BACKUP_DIR = DATA_DIR / "backups"
AUTO_REINDEX_ON_UPDATE = True
# ======== UI SETTINGS ========
DEFAULT_THEME = "light"
AVAILABLE_THEMES = ["light", "dark", "blue", "green"]

# ======== EMAIL SETTINGS ========
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")
SENDER_EMAIL = os.getenv("SENDER_EMAIL", "")

# HR escalation settings
HR_EMAILS = os.getenv("HR_EMAILS", "").split(",") if os.getenv("HR_EMAILS") else []
ENABLE_EMAIL_ESCALATION = os.getenv("ENABLE_EMAIL_ESCALATION", "false").lower() == "true"

# ======== LOGGING CONFIGURATION ========
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
ENABLE_MODEL_LOADING_LOGS = os.getenv("ENABLE_MODEL_LOADING_LOGS", "true").lower() == "true"

# ======== DEVELOPMENT SETTINGS ========
DEBUG_MODE = os.getenv("FLASK_DEBUG", "true").lower() == "true"
SKIP_MODEL_VALIDATION = os.getenv("SKIP_MODEL_VALIDATION", "false").lower() == "true"
MOCKAPI_URL = os.getenv("MOCKAPI_URL", "http://localhost:5005/mockapi")