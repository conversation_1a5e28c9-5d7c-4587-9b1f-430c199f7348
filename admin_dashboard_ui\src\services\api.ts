import axios from "axios";
import { API_BASE_URL } from "../apiConfig";

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true,
});

// Add interceptors for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Optionally, add global error handling/logging here
    return Promise.reject(error);
  }
);

export default api;

export const get = (url: string, config?: any) => api.get(url, config);
export const post = (url: string, data?: any, config?: any) => api.post(url, data, config);

// Custom API helpers matching codebase usage
export const requestOtp = (email: string) => api.post('/auth/request-otp', { email });
export const login = (email: string, otp: string) => api.post('/auth/login', { email, otp });
export const changeRole = (email: string, newRole: string) => api.post('/admin/change-role', { email, role: newRole });
export const getSessions = () => api.get('/sessions');
export const revokeSession = (token_hash: string, email: string, expires_at: string) => api.post('/sessions/revoke', { token_hash, email, expires_at });
export const getDeviceLogs = () => api.get('/devices/logs');
export const getAuditLogs = () => api.get('/audit/logs');
export const getStatsUsers = () => api.get('/stats/users');
export const getStatsUsage = () => api.get('/stats/usage');
export const getStatsIntents = () => api.get('/stats/intents');
export const getAllChats = () => api.get('/api/all-chats');
export const getChatLogs = () => api.get('/api/chatlogs');
export const getQueries = () => api.get('/api/queries');
export const getChatTrends = () => api.get('/api/chat-trends');
export const getChatTypes = () => api.get('/api/chat-types');
export const getOverrides = () => api.get('/api/overrides');
export const getUserInfo = () => api.get('/api/user');
export const getHRRepresentatives = () => api.get('/api/hr-representatives');
export const getDocumentHealth = () => api.get('/api/health/documents'); 