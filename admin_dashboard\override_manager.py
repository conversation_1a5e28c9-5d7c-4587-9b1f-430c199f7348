import sqlite3
from typing import Optional
import os

DB_PATH = os.path.join(os.path.dirname(__file__), 'overrides.db')

def init_db():
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS overrides (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question_pattern TEXT UNIQUE,
            override_response TEXT
        )''')
        conn.commit()

def add_override(question_pattern: str, override_response: str):
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute('''INSERT OR REPLACE INTO overrides (question_pattern, override_response) VALUES (?, ?)''', (question_pattern, override_response))
        conn.commit()

def get_override(question: str) -> Optional[str]:
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute('''SELECT override_response FROM overrides WHERE ? LIKE question_pattern''', (question,))
        row = c.fetchone()
        return row[0] if row else None

def list_overrides():
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute('SELECT id, question_pattern, override_response FROM overrides')
        return c.fetchall()

def delete_override(override_id: int):
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute('DELETE FROM overrides WHERE id=?', (override_id,))
        conn.commit()

# For future: Add admin CRUD UI, integrate with chatbot pre-RAG logic

init_db() 