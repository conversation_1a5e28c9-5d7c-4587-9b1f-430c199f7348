import React, { createContext, useContext, useEffect, useState } from 'react';

interface PermissionsContextType {
  role: string;
  roleLevel: number;
  permissions: string[];
  loading: boolean;
}

export const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

export const PermissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [role, setRole] = useState('');
  const [roleLevel, setRoleLevel] = useState(0);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchPermissions() {
      setLoading(true);
      try {
        // Replace with real endpoint when available
        const res = await fetch('/api/admin-users/me');
        if (!res.ok) throw new Error('Failed to fetch user info');
        const data = await res.json();
        setRole(data.role);
        setRoleLevel(data.role_level || data.roleLevel || 0);
        setPermissions(data.permissions || []);
      } catch (e) {
        // Fallback/mock for local dev
        setRole('Superadmin');
        setRoleLevel(3);
        setPermissions(['manage_users', 'view_analytics', 'edit_settings']);
      } finally {
        setLoading(false);
      }
    }
    fetchPermissions();
  }, []);

  return (
    <PermissionsContext.Provider value={{ role, roleLevel, permissions, loading }}>
      {children}
    </PermissionsContext.Provider>
  );
}; 