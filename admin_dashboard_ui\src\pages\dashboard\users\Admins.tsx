import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select"; // Assume stub
import { Skeleton } from "@/components/ui/skeleton";
import { useReactTable, getCoreRowModel, flexRender, ColumnDef, getSortedRowModel, SortingState } from "@tanstack/react-table";
import api from "@/services/api";

const PAGE_SIZE = 10;
const ROLES = ["All", "superadmin", "viewer", "trainer", "auditor"];
const STATUS = ["All", "Active", "Inactive"];

// Define the type for a user row
interface UserRow {
  id: string;
  user_email: string;
  role: string;
  last_updated: string;
  [key: string]: any;
}

const columns: ColumnDef<UserRow>[] = [
  { accessorKey: "user_email", header: "User Email" },
  { accessorKey: "role", header: "Role" },
  { accessorKey: "last_updated", header: "Last Updated" },
  { accessorKey: "action", header: "Action" },
];

const RBACPanel = () => {
  const [roleFilter, setRoleFilter] = useState("All");
  const [status, setStatus] = useState("All");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<UserRow[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [editRole, setEditRole] = useState<{ [id: string]: string }>({});
  const [updating, setUpdating] = useState<{ [id: string]: boolean }>({});

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      roleFilter !== "All" ? `role=${encodeURIComponent(roleFilter)}` : null,
      status !== "All" ? `status=${encodeURIComponent(status)}` : null,
      `page=${page}`,
      `pageSize=${PAGE_SIZE}`,
    ]
      .filter(Boolean)
      .join("&");
    api
      .get(`/admin/users?${params}`)
      .then((res) => {
        setData(res.data.users || []);
        setTotal(res.data.total || 0);
      })
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [roleFilter, status, page]);

  const handleRoleChange = (id: string, newRole: string) => {
    setEditRole((prev) => ({ ...prev, [id]: newRole }));
  };
  const handleUpdateRole = (id: string) => {
    setUpdating((prev) => ({ ...prev, [id]: true }));
    api
      .patch(`/admin/users/update-role`, { id, role: editRole[id] })
      .then(() => {
        setData((prev) =>
          prev.map((u) => (u.id === id ? { ...u, role: editRole[id], last_updated: new Date().toISOString() } : u))
        );
      })
      .catch(() => alert("Failed to update role (mock)"))
      .finally(() => setUpdating((prev) => ({ ...prev, [id]: false })));
  };

  const table = useReactTable({
    data,
    columns: columns.map(col =>
      col.id === "role"
        ? {
            ...col,
            cell: ({ row }) => {
              const user = row.original as UserRow;
              return (
                <Select
                  value={editRole[user.id] ?? user.role}
                  onValueChange={val => handleRoleChange(user.id, val)}
                >
                  {ROLES.filter(r => r !== "All").map(opt => (
                    <option key={opt} value={opt}>{opt}</option>
                  ))}
                </Select>
              );
            },
          }
        : col.id === "action"
        ? {
            ...col,
            cell: ({ row }) => {
              const user = row.original as UserRow;
              return (
                <Button
                  size="sm"
                  variant="outline"
                  disabled={updating[user.id] || (editRole[user.id] ?? user.role) === user.role}
                  onClick={() => handleUpdateRole(user.id)}
                >
                  {updating[user.id] ? "Updating..." : "Update Role"}
                </Button>
              );
            },
          }
        : col
    ) as ColumnDef<UserRow, any>[],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false,
  });

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div className="max-w-5xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Role-Based Access Control (RBAC)</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              {ROLES.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Select value={status} onValueChange={setStatus}>
              {STATUS.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
              <thead className="bg-muted dark:bg-zinc-800">
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th
                        key={header.id}
                        className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase cursor-pointer"
                        onClick={header.column.getToggleSortingHandler?.()}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getIsSorted?.() ? (header.column.getIsSorted() === "asc" ? " ▲" : " ▼") : ""}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="bg-background dark:bg-zinc-900">
                {table.getRowModel().rows.map(row => (
                  <tr key={row.id} className="border-b border-border dark:border-zinc-800">
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className="px-4 py-2 text-sm">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {/* Pagination controls */}
          <div className="flex justify-end gap-2 items-center mt-4">
            <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {page} of {totalPages || 1}
            </span>
            <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RBACPanel; 