import pyotp
import qrcode
from io import BytesIO
from .hrms import mock_hrms_api  # if needed for user lookup
from src.database.user_db import UserModel

def generate_secret(email: str) -> str:
    return pyotp.random_base32()

def get_qr_code(email: str, secret: str) -> BytesIO:
    totp = pyotp.TOTP(secret)
    uri = totp.provisioning_uri(name=email, issuer_name="Ziantrix Admin")
    img = qrcode.make(uri)
    buf = BytesIO()
    img.save(buf)
    buf.seek(0)
    return buf

def is_2fa_enabled(email: str) -> bool:
    user_model = UserModel()
    user = user_model.get_user_by_email(email)
    return bool(user and user.get('two_fa_secret')) 