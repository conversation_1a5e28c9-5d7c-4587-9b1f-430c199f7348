import { create } from "zustand";
import { persist } from "zustand/middleware";

type AuthState = {
  role: "superadmin" | "viewer";
  isAuthenticated: boolean;
  user: {
    id: string;
    email: string;
    name: string;
  } | null;
  setRole: (role: "superadmin" | "viewer") => void;
  login: (user: { id: string; email: string; name: string; role: "superadmin" | "viewer" }) => void;
  logout: () => void;
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      role: "superadmin",
      isAuthenticated: false,
      user: null,
      setRole: (role: "superadmin" | "viewer") => set({ role }),
      login: (user) => set({ 
        user: { id: user.id, email: user.email, name: user.name },
        role: user.role,
        isAuthenticated: true 
      }),
      logout: () => set({ 
        user: null, 
        isAuthenticated: false,
        role: "superadmin" // Reset to default role
      }),
    }),
    {
      name: "auth-storage",
    }
  )
); 