import { useEffect, useState } from 'react';
import api from '../../services/api';
import { changeRole } from "@/services/api";
import { AdminUser, Role } from '../../types';
import { Layout } from '../../components/Layout';
import { RoleBadge } from '../../components/RoleBadge';
import { ProtectedRoute } from '../../components/ProtectedRoute';

const roleOptions: Role[] = ['superadmin', 'admin', 'hr_lead', 'viewer'];

export default function UsersPage() {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [updating, setUpdating] = useState<string | null>(null);

  useEffect(() => {
    // TODO: Replace with real API call to fetch admin users
    setLoading(false);
    setUsers([
      { email: '<EMAIL>', role: 'superadmin', active: true, full_name: 'Super Admin' },
      { email: '<EMAIL>', role: 'hr_lead', active: true, full_name: 'HR Lead' },
      { email: '<EMAIL>', role: 'viewer', active: true, full_name: 'Viewer' },
      { email: '<EMAIL>', role: 'admin', active: false, full_name: 'Admin User' },
    ]);
  }, []);

  const handleRoleChange = async (email: string, newRole: Role) => {
    setUpdating(email);
    try {
      await changeRole(email, newRole);
      setUsers(users => users.map(u => u.email === email ? { ...u, role: newRole } : u));
    } catch {
      setError('Failed to update role');
    } finally {
      setUpdating(null);
    }
  };

  const handleDeactivate = async (email: string) => {
    // TODO: Implement deactivate API
    setUsers(users => users.map(u => u.email === email ? { ...u, active: false } : u));
  };

  return (
    <ProtectedRoute roles={['superadmin']}>
      <h1 className="text-2xl font-bold mb-6">Admin Users</h1>
      {error && <div className="text-red-500 mb-4">{error}</div>}
      <div className="overflow-x-auto">
        <table className="min-w-full bg-neutral-900 text-neutral-100 rounded shadow border border-neutral-800">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left">Email</th>
              <th className="px-4 py-2">Name</th>
              <th className="px-4 py-2">Role</th>
              <th className="px-4 py-2">Status</th>
              <th className="px-4 py-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr><td colSpan={5} className="text-center py-8">Loading...</td></tr>
            ) : users.map(user => (
              <tr key={user.email} className={!user.active ? 'bg-neutral-800' : ''}>
                <td className="px-4 py-2 font-mono">{user.email}</td>
                <td className="px-4 py-2">{user.full_name}</td>
                <td className="px-4 py-2"><RoleBadge role={user.role} /></td>
                <td className="px-4 py-2">{user.active ? 'Active' : 'Inactive'}</td>
                <td className="px-4 py-2 flex gap-2">
                  <select
                    value={user.role}
                    onChange={e => handleRoleChange(user.email, e.target.value as Role)}
                    disabled={updating === user.email}
                    className="border border-neutral-700 rounded px-2 py-1 bg-neutral-900 text-neutral-100 disabled:bg-neutral-800 disabled:text-neutral-500"
                  >
                    {roleOptions.map(r => <option key={r} value={r}>{r}</option>)}
                  </select>
                  <button
                    onClick={() => handleDeactivate(user.email)}
                    disabled={!user.active || updating === user.email}
                    className="px-2 py-1 bg-neutral-800 border border-neutral-700 rounded hover:bg-neutral-700 text-xs text-neutral-100 disabled:bg-neutral-900 disabled:text-neutral-500"
                  >
                    Deactivate
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </ProtectedRoute>
  );
} 