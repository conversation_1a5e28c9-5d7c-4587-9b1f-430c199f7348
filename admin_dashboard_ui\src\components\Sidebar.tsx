import React from "react";
import { NavLink } from "react-router-dom";
import { motion } from "framer-motion";
import { useSidebarStore } from "../hooks/useSidebarStore";
import { useAuthStore } from "../hooks/useAuthStore";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Menu, ChevronLeft, ChevronRight, LayoutDashboard } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Icons for each section
import {
  BarChart3,
  MessageSquare,
  Brain,
  Shield,
  Monitor,
  Users,
  Settings,
  TrendingUp,
  AlertTriangle,
  Clock,
  MapPin,
  FileText,
  Trash2,
  Eye,
  UserCheck,
  Palette,
  Mail,
  Zap,
  Target,
  Activity,
  Headphones,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";

const sidebarStructure = [
  {
    label: "Analytics",
    icon: BarChart3,
    value: "analytics",
    children: [
      { 
        label: "Overview", 
        path: "/analytics/overview", 
        roles: ["superadmin", "viewer"],
        icon: TrendingUp
      },
      {
        label: "Insights",
        path: "/analytics/insights",
        roles: ["superadmin", "viewer"],
        icon: BarChart3
      },
    ],
  },
  {
    label: "Feedback & Escalations",
    icon: MessageSquare,
    value: "feedback",
    children: [
      { 
        label: "Feedback Trends", 
        path: "/feedback/trends", 
        roles: ["superadmin", "viewer"],
        icon: TrendingUp
      },
      { 
        label: "Escalated Chats", 
        path: "/feedback/escalated", 
        roles: ["superadmin", "viewer"],
        icon: AlertTriangle
      },
      { 
        label: "Resolution Timeline", 
        path: "/feedback/resolution", 
        roles: ["superadmin"],
        icon: CheckCircle
      },
    ],
  },
  {
    label: "AI Insights",
    icon: Brain,
    value: "ai",
    children: [
      { 
        label: "Weekly AI Digest", 
        path: "/ai/weekly-digest", 
        roles: ["superadmin", "viewer"],
        icon: FileText
      },
      { 
        label: "Policy Drift Detection", 
        path: "/ai/policy-drift", 
        roles: ["superadmin"],
        icon: XCircle
      },
    ],
  },
  {
    label: "Live Support",
    icon: Headphones,
    value: "live",
    children: [
      { 
        label: "Queue", 
        path: "/live-support/queue", 
        roles: ["superadmin", "viewer"],
        icon: Clock
      },
      { 
        label: "Ongoing", 
        path: "/live-support/ongoing", 
        roles: ["superadmin", "viewer"],
        icon: Activity
      },
    ],
  },
  {
    label: "Compliance & Auditing",
    icon: Shield,
    value: "compliance",
    children: [
      { 
        label: "GDPR & Consent Logs", 
        path: "/compliance/gdpr", 
        roles: ["superadmin"],
        icon: Shield
      },
      { 
        label: "Data Deletion Requests", 
        path: "/compliance/deletion", 
        roles: ["superadmin"],
        icon: Trash2
      },
      { 
        label: "Sensitive Data Flags", 
        path: "/compliance/sensitive", 
        roles: ["superadmin"],
        icon: Eye
      },
    ],
  },
  {
    label: "Device Intelligence",
    icon: Monitor,
    value: "device-intelligence",
    children: [
      { 
        label: "Device Intelligence",
        path: "/device-intelligence",
        roles: ["superadmin", "viewer"],
        icon: Monitor
      },
    ],
  },
  {
    label: "Users",
    icon: Users,
    value: "users",
    children: [
      {
        label: "Admin Users & Roles",
        path: "/admin-users-roles",
        roles: ["superadmin"],
        icon: Users
      },
    ],
  },
  {
    label: "Settings",
    icon: Settings,
    value: "settings",
    children: [
      { 
        label: "Theme Toggle", 
        path: "/settings/theme", 
        roles: ["superadmin", "viewer"],
        icon: Palette
      },
      { 
        label: "Email Scheduling", 
        path: "/settings/email", 
        roles: ["superadmin"],
        icon: Mail
      },
    ],
  },
];

export const Sidebar = () => {
  const { sidebarOpen, toggleSidebar } = useSidebarStore();
  const { role } = useAuthStore();

  const renderNavItem = (item: any, isCollapsed: boolean) => {
    const IconComponent = item.icon;
    
    if (isCollapsed) {
      return (
        <TooltipProvider key={item.path}>
          <Tooltip>
            <TooltipTrigger asChild>
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center justify-center w-10 h-10 rounded-lg transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary/50 ${
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted hover:text-accent-foreground"
                  }`
                }
                tabIndex={0}
                aria-label={item.label}
              >
                <IconComponent className="h-4 w-4" />
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>{item.label}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <NavLink
        key={item.path}
        to={item.path}
        className={({ isActive }) =>
          `flex items-center gap-3 px-3 py-2 rounded-lg transition-colors duration-150 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50 ${
            isActive
              ? "bg-primary text-primary-foreground"
              : "hover:bg-muted hover:text-accent-foreground"
          }`
        }
        tabIndex={0}
        aria-label={item.label}
      >
        <IconComponent className="h-4 w-4" />
        <span className="text-sm font-medium">{item.label}</span>
      </NavLink>
    );
  };

  return (
    <aside
      className={`fixed top-0 left-0 h-full bg-background border-r border-border shadow-lg z-40 flex flex-col dark:bg-zinc-900 dark:border-zinc-800 transition-all duration-300`}
      style={{ width: sidebarOpen ? "299px" : "74px" }}
      tabIndex={-1}
      aria-label="Sidebar navigation"
    >
      {sidebarOpen ? (
        // Expanded: logo left, menu icon right
        <div className="flex items-center justify-between px-4 py-3">
          <img
            src={process.env.PUBLIC_URL + '/logo192.png'}
            alt="ZiaHR Logo"
            className="w-8 h-8 rounded-lg shadow"
          />
          <button
            onClick={toggleSidebar}
            aria-label={sidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
            className="focus:outline-none hover:bg-muted rounded-lg p-2 transition-colors duration-150 ml-2"
            style={{ background: 'none', border: 'none' }}
          >
            <LayoutDashboard className="h-6 w-6 text-blue-600" />
          </button>
        </div>
      ) : (
        // Collapsed: logo at top center, clickable
        <div className="flex flex-col items-center pt-4 pb-2">
          <button
            onClick={toggleSidebar}
            aria-label="Expand sidebar"
            className="focus:outline-none"
            style={{ background: 'none', border: 'none', padding: 0, margin: 0 }}
          >
            <img
              src={process.env.PUBLIC_URL + '/logo192.png'}
              alt="ZiaHR Logo"
              className="w-8 h-8 rounded-lg shadow"
            />
          </button>
        </div>
      )}
      {/* Navigation */}
      {sidebarOpen && (
        <nav className="flex-1 overflow-y-auto py-4 px-2">
          {/* Expanded sidebar with accordion */}
          <Accordion type="multiple" className="space-y-2">
            {sidebarStructure.map((section) => {
              const SectionIcon = section.icon;
              const filteredChildren = section.children.filter((item) =>
                item.roles.includes(role)
              );

              if (filteredChildren.length === 0) return null;

              return (
                <AccordionItem
                  key={section.value}
                  value={section.value}
                  className="border-none"
                >
                  <AccordionTrigger className="flex items-center gap-3 px-3 py-2 hover:bg-muted rounded-lg transition-colors duration-150 [&[data-state=open]>svg]:rotate-180">
                    <SectionIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">{section.label}</span>
                  </AccordionTrigger>
                  <AccordionContent className="pt-2">
                    <div className="space-y-1 pl-7">
                      {filteredChildren.map((item) => renderNavItem(item, false))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
        </nav>
      )}
    </aside>
  );
}; 